[INFO] 2025-06-26 11:37:38.290 Aws_Init_Cleanup [139934512894016] Initiate AWS SDK for C++ with Version:1.9.234
[INFO] 2025-06-26 11:37:38.290 Aws::Config::AWSConfigFileProfileConfigLoader [139934512894016] Initializing config loader against fileName /home/<USER>/.aws/credentials and using profilePrefix = 0
[INFO] 2025-06-26 11:37:38.290 Aws::Config::AWSConfigFileProfileConfigLoader [139934512894016] Initializing config loader against fileName /home/<USER>/.aws/config and using profilePrefix = 1
[INFO] 2025-06-26 11:37:38.290 Aws::Config::AWSProfileConfigLoader [139934512894016] Successfully reloaded configuration.
[INFO] 2025-06-26 11:37:38.290 Aws::Config::AWSProfileConfigLoader [139934512894016] Successfully reloaded configuration.
[INFO] 2025-06-26 11:37:38.290 event-loop [139934512894016] id=0x55b5d851b7b0: Initializing edge-triggered epoll
[INFO] 2025-06-26 11:37:38.290 event-loop [139934512894016] id=0x55b5d851b7b0: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 11:37:38.290 event-loop [139934512894016] id=0x55b5d851b7b0: Starting event-loop thread.
[INFO] 2025-06-26 11:37:38.290 event-loop [139934512894016] id=0x55b5d853c630: Initializing edge-triggered epoll
[INFO] 2025-06-26 11:37:38.290 event-loop [139934512894016] id=0x55b5d853c630: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 11:37:38.290 event-loop [139934512894016] id=0x55b5d853c630: Starting event-loop thread.
[INFO] 2025-06-26 11:37:38.290 event-loop [139934512894016] id=0x55b5d8515d70: Initializing edge-triggered epoll
[INFO] 2025-06-26 11:37:38.290 event-loop [139934512894016] id=0x55b5d8515d70: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 11:37:38.290 event-loop [139934512894016] id=0x55b5d8515d70: Starting event-loop thread.
[INFO] 2025-06-26 11:37:38.290 event-loop [139934504486656] id=0x55b5d851b7b0: main loop started
[INFO] 2025-06-26 11:37:38.290 event-loop [139934512894016] id=0x55b5d8509340: Initializing edge-triggered epoll
[INFO] 2025-06-26 11:37:38.290 event-loop [139934504486656] id=0x55b5d851b7b0: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 11:37:38.290 event-loop [139934512894016] id=0x55b5d8509340: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 11:37:38.290 event-loop [139934512894016] id=0x55b5d8509340: Starting event-loop thread.
[INFO] 2025-06-26 11:37:38.290 event-loop [139934496093952] id=0x55b5d853c630: main loop started
[INFO] 2025-06-26 11:37:38.290 event-loop [139934496093952] id=0x55b5d853c630: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 11:37:38.290 event-loop [139934512894016] id=0x55b5d8501050: Initializing edge-triggered epoll
[INFO] 2025-06-26 11:37:38.290 event-loop [139934512894016] id=0x55b5d8501050: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 11:37:38.290 event-loop [139934512894016] id=0x55b5d8501050: Starting event-loop thread.
[INFO] 2025-06-26 11:37:38.290 event-loop [139934487701248] id=0x55b5d8515d70: main loop started
[INFO] 2025-06-26 11:37:38.290 event-loop [139934487701248] id=0x55b5d8515d70: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 11:37:38.290 event-loop [139934512894016] id=0x55b5d84f0540: Initializing edge-triggered epoll
[INFO] 2025-06-26 11:37:38.290 event-loop [139934512894016] id=0x55b5d84f0540: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 11:37:38.290 event-loop [139934512894016] id=0x55b5d84f0540: Starting event-loop thread.
[INFO] 2025-06-26 11:37:38.290 event-loop [139934479308544] id=0x55b5d8509340: main loop started
[INFO] 2025-06-26 11:37:38.290 dns [139934512894016] id=0x55b5d85bb8e0: Initializing default host resolver with 8 max host entries.
[INFO] 2025-06-26 11:37:38.290 event-loop [139934479308544] id=0x55b5d8509340: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 11:37:38.290 channel-bootstrap [139934512894016] id=0x55b5d85bb980: Initializing client bootstrap with event-loop group 0x55b5d854af80
[INFO] 2025-06-26 11:37:38.290 event-loop [139934253967104] id=0x55b5d84f0540: main loop started
[INFO] 2025-06-26 11:37:38.290 event-loop [139934253967104] id=0x55b5d84f0540: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 11:37:38.290 event-loop [139934262359808] id=0x55b5d8501050: main loop started
[INFO] 2025-06-26 11:37:38.290 event-loop [139934262359808] id=0x55b5d8501050: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 11:37:38.314 CurlHttpClient [139934512894016] Initializing Curl library with version: 7.86.0, ssl version: OpenSSL/3.1.2
[INFO] 2025-06-26 11:37:38.314 EC2MetadataClient [139934512894016] Using IMDS endpoint: http://***************
[WARN] 2025-06-26 11:37:38.314 ClientConfiguration [139934512894016] Retry Strategy will use the default max attempts.
[INFO] 2025-06-26 11:37:38.314 EC2MetadataClient [139934512894016] Creating AWSHttpResourceClient with max connections 2 and scheme http
[INFO] 2025-06-26 11:37:38.314 CurlHandleContainer [139934512894016] Initializing CurlHandleContainer with size 2
[WARN] 2025-06-26 11:37:38.314 ClientConfiguration [139934512894016] Retry Strategy will use the default max attempts.
[INFO] 2025-06-26 11:37:38.314 Aws::Config::AWSConfigFileProfileConfigLoader [139934512894016] Initializing config loader against fileName /home/<USER>/.aws/credentials and using profilePrefix = 0
[INFO] 2025-06-26 11:37:38.314 ProfileConfigFileAWSCredentialsProvider [139934512894016] Setting provider to read credentials from /home/<USER>/.aws/credentials for credentials file and /home/<USER>/.aws/config for the config file , for use with profile default
[INFO] 2025-06-26 11:37:38.314 ProcessCredentialsProvider [139934512894016] Setting process credentials provider to read config from default
[WARN] 2025-06-26 11:37:38.314 STSAssumeRoleWithWebIdentityCredentialsProvider [139934512894016] Token file must be specified to use STS AssumeRole web identity creds provider.
[INFO] 2025-06-26 11:37:38.314 SSOCredentialsProvider [139934512894016] Setting sso credentials provider to read config from default
[INFO] 2025-06-26 11:37:38.314 InstanceProfileCredentialsProvider [139934512894016] Creating Instance with default EC2MetadataClient and refresh rate 300000
[INFO] 2025-06-26 11:37:38.314 DefaultAWSCredentialsProviderChain [139934512894016] Added EC2 metadata service credentials provider to the provider chain.
[INFO] 2025-06-26 11:37:38.314 Aws::Config::AWSProfileConfigLoader [139934512894016] Successfully reloaded configuration.
[INFO] 2025-06-26 11:37:38.315 CurlHandleContainer [139934512894016] Initializing CurlHandleContainer with size 25
[INFO] 2025-06-26 11:37:38.315 CurlHandleContainer [139934237181696] Pool grown by 2
[INFO] 2025-06-26 11:37:38.315 CurlHandleContainer [139934237181696] Connection has been released. Continuing.
[INFO] 2025-06-26 11:37:38.325 CurlHandleContainer [139934245574400] Connection has been released. Continuing.
[INFO] 2025-06-26 11:37:38.335 CurlHandleContainer [139934228788992] Pool grown by 4
[INFO] 2025-06-26 11:37:38.335 CurlHandleContainer [139934228788992] Connection has been released. Continuing.
[INFO] 2025-06-26 11:37:38.345 CurlHandleContainer [139934220396288] Connection has been released. Continuing.
[WARN] 2025-06-26 11:37:38.931 AWSErrorMarshaller [139934237181696] Encountered Unknown AWSError 'PermanentRedirect': The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
[ERROR] 2025-06-26 11:37:38.931 AWSClient [139934237181696] HTTP response code: 301
Resolved remote host IP address: **************
Request ID: WQ14NXPAHPMH8VD8
Exception name: PermanentRedirect
Error message: Unable to parse ExceptionName: PermanentRedirect Message: The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
7 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 11:37:38 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-bucket-region : eu-central-1
x-amz-id-2 : K1Eq82QeBuZdZTuubOVv9ygIDyt7qKZPKflsOWuZ4LuEYUFpfs34l90pkraZngJB7sufaFhMGW8=
x-amz-request-id : WQ14NXPAHPMH8VD8
[WARN] 2025-06-26 11:37:38.931 AWSClient [139934237181696] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 11:37:38.932 CurlHandleContainer [139934237181696] Connection has been released. Continuing.
[WARN] 2025-06-26 11:37:38.936 AWSErrorMarshaller [139934220396288] Encountered Unknown AWSError 'PermanentRedirect': The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
[ERROR] 2025-06-26 11:37:38.936 AWSClient [139934220396288] HTTP response code: 301
Resolved remote host IP address: **************
Request ID: WQ1D144F98Z3AG1Q
Exception name: PermanentRedirect
Error message: Unable to parse ExceptionName: PermanentRedirect Message: The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
7 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 11:37:38 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-bucket-region : eu-central-1
x-amz-id-2 : wBgAWpg7HBBxlrqxD3ZyGyipft2QiqRSNyKzyTnAz+pT3tXbgzXBaH059+BUbx7lpoz7AM2HdQo=
x-amz-request-id : WQ1D144F98Z3AG1Q
[WARN] 2025-06-26 11:37:38.936 AWSClient [139934220396288] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 11:37:38.936 CurlHandleContainer [139934220396288] Connection has been released. Continuing.
[WARN] 2025-06-26 11:37:38.943 AWSErrorMarshaller [139934228788992] Encountered Unknown AWSError 'PermanentRedirect': The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
[ERROR] 2025-06-26 11:37:38.943 AWSClient [139934228788992] HTTP response code: 301
Resolved remote host IP address: **************
Request ID: WQ169DSD4X5T6STQ
Exception name: PermanentRedirect
Error message: Unable to parse ExceptionName: PermanentRedirect Message: The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
7 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 11:37:38 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-bucket-region : eu-central-1
x-amz-id-2 : 7WB/9h2hwX0Di7PFxwbm04j4cFQUAD4g4QweaecaSfMe9jxDg56xXVu/cUTZb221aNYMjX5INfY=
x-amz-request-id : WQ169DSD4X5T6STQ
[WARN] 2025-06-26 11:37:38.943 AWSClient [139934228788992] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 11:37:38.943 CurlHandleContainer [139934228788992] Connection has been released. Continuing.
[WARN] 2025-06-26 11:37:38.949 AWSErrorMarshaller [139934245574400] Encountered Unknown AWSError 'PermanentRedirect': The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
[ERROR] 2025-06-26 11:37:38.949 AWSClient [139934245574400] HTTP response code: 301
Resolved remote host IP address: **************
Request ID: WQ12RHA06CTFQ2SX
Exception name: PermanentRedirect
Error message: Unable to parse ExceptionName: PermanentRedirect Message: The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
7 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 11:37:38 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-bucket-region : eu-central-1
x-amz-id-2 : upJNZAEZV9qKSrOF5pw1eyMf4VLXxrVA6PoZoSBesi5VyD6jLxKQyxlYy3+VZwfhXZy+/WD8kdA=
x-amz-request-id : WQ12RHA06CTFQ2SX
[WARN] 2025-06-26 11:37:38.949 AWSClient [139934245574400] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 11:37:38.949 CurlHandleContainer [139934245574400] Connection has been released. Continuing.
[WARN] 2025-06-26 11:37:39.128 AWSErrorMarshaller [139934220396288] Encountered Unknown AWSError 'PermanentRedirect': The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
[ERROR] 2025-06-26 11:37:39.128 AWSClient [139934220396288] HTTP response code: 301
Resolved remote host IP address: **************
Request ID: TSWK6T45Q5MAT59A
Exception name: PermanentRedirect
Error message: Unable to parse ExceptionName: PermanentRedirect Message: The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
7 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 11:37:38 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-bucket-region : eu-central-1
x-amz-id-2 : YIReoN52So/72CYO1e254ApMXUZralZGpa51FPylwMnIeb8pXwx61DxO0uZTaiKWcxts54jmLGU=
x-amz-request-id : TSWK6T45Q5MAT59A
[WARN] 2025-06-26 11:37:39.128 AWSClient [139934220396288] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[WARN] 2025-06-26 11:37:39.129 AWSErrorMarshaller [139934237181696] Encountered Unknown AWSError 'PermanentRedirect': The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
[ERROR] 2025-06-26 11:37:39.129 AWSClient [139934237181696] HTTP response code: 301
Resolved remote host IP address: **************
Request ID: TSWPBCPF28EP6GA7
Exception name: PermanentRedirect
Error message: Unable to parse ExceptionName: PermanentRedirect Message: The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
7 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 11:37:38 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-bucket-region : eu-central-1
x-amz-id-2 : fgyWyGxsyLg2cqkpbuKOiC5CdR7jLdAM2TEf3zeUcLzlbNTwfoVPT/HS+9ohyxGOXH6KieYmXaQ=
x-amz-request-id : TSWPBCPF28EP6GA7
[WARN] 2025-06-26 11:37:39.129 AWSClient [139934237181696] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 11:37:39.129 CurlHandleContainer [139934220396288] Connection has been released. Continuing.
[INFO] 2025-06-26 11:37:39.129 CurlHandleContainer [139934237181696] Connection has been released. Continuing.
[WARN] 2025-06-26 11:37:39.140 AWSErrorMarshaller [139934228788992] Encountered Unknown AWSError 'PermanentRedirect': The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
[ERROR] 2025-06-26 11:37:39.140 AWSClient [139934228788992] HTTP response code: 301
Resolved remote host IP address: **************
Request ID: TSWY1KRF4XYS6DN4
Exception name: PermanentRedirect
Error message: Unable to parse ExceptionName: PermanentRedirect Message: The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
7 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 11:37:38 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-bucket-region : eu-central-1
x-amz-id-2 : uOiPr/ps686teXlYdkE3+KTlXtQg1G4UKIUPO2AVwFYX9Qak4gM3FgLarI+9hst1JKKb6NLO21o=
x-amz-request-id : TSWY1KRF4XYS6DN4
[WARN] 2025-06-26 11:37:39.140 AWSClient [139934228788992] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[WARN] 2025-06-26 11:37:39.150 AWSErrorMarshaller [139934245574400] Encountered Unknown AWSError 'PermanentRedirect': The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
[ERROR] 2025-06-26 11:37:39.150 AWSClient [139934245574400] HTTP response code: 301
Resolved remote host IP address: **************
Request ID: TSWHTGQNQP6R141D
Exception name: PermanentRedirect
Error message: Unable to parse ExceptionName: PermanentRedirect Message: The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
7 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 11:37:38 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-bucket-region : eu-central-1
x-amz-id-2 : jr1a9iNDUavBp+xp1Ds3gJVQhUVibNuWPbM+9cJZBbbBP0iJZkE6/STZ9ABUYY54PGX+HlkgbFc=
x-amz-request-id : TSWHTGQNQP6R141D
[WARN] 2025-06-26 11:37:39.150 AWSClient [139934245574400] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[WARN] 2025-06-26 11:37:39.318 AWSErrorMarshaller [139934237181696] Encountered Unknown AWSError 'PermanentRedirect': The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
[ERROR] 2025-06-26 11:37:39.318 AWSClient [139934237181696] HTTP response code: 301
Resolved remote host IP address: **************
Request ID: TSWRG1TJB4Y90235
Exception name: PermanentRedirect
Error message: Unable to parse ExceptionName: PermanentRedirect Message: The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
7 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 11:37:38 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-bucket-region : eu-central-1
x-amz-id-2 : 5lUfP62w5cfb2CfAULRJqJ2dggHT7z9jWmQnth4XiEnYwF/atJ1wjsInMeN7IoRzkunLk7H91/4=
x-amz-request-id : TSWRG1TJB4Y90235
[WARN] 2025-06-26 11:37:39.318 AWSClient [139934237181696] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[WARN] 2025-06-26 11:37:39.325 AWSErrorMarshaller [139934220396288] Encountered Unknown AWSError 'PermanentRedirect': The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
[ERROR] 2025-06-26 11:37:39.325 AWSClient [139934220396288] HTTP response code: 301
Resolved remote host IP address: **************
Request ID: TSWQQ0C9ZY4QYSR4
Exception name: PermanentRedirect
Error message: Unable to parse ExceptionName: PermanentRedirect Message: The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
7 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 11:37:38 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-bucket-region : eu-central-1
x-amz-id-2 : opzw6mYqTpDgoim3AfNDydgcKaTPWKwOcua8E4Am3bo5QM29pMjaodesOz5H9HUU/8Y6j9o2V3Q=
x-amz-request-id : TSWQQ0C9ZY4QYSR4
[WARN] 2025-06-26 11:37:39.325 AWSClient [139934220396288] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 11:37:39.325 CurlHandleContainer [139934512894016] Cleaning up CurlHandleContainer.
[INFO] 2025-06-26 11:37:41.332 Aws::Config::AWSConfigFileProfileConfigLoader [139934512894016] Initializing config loader against fileName /home/<USER>/.aws/credentials and using profilePrefix = 0
[INFO] 2025-06-26 11:37:41.332 ProfileConfigFileAWSCredentialsProvider [139934512894016] Setting provider to read credentials from /home/<USER>/.aws/credentials for credentials file and /home/<USER>/.aws/config for the config file , for use with profile default
[INFO] 2025-06-26 11:37:41.332 ProcessCredentialsProvider [139934512894016] Setting process credentials provider to read config from default
[WARN] 2025-06-26 11:37:41.332 STSAssumeRoleWithWebIdentityCredentialsProvider [139934512894016] Token file must be specified to use STS AssumeRole web identity creds provider.
[INFO] 2025-06-26 11:37:41.332 SSOCredentialsProvider [139934512894016] Setting sso credentials provider to read config from default
[INFO] 2025-06-26 11:37:41.332 InstanceProfileCredentialsProvider [139934512894016] Creating Instance with default EC2MetadataClient and refresh rate 300000
[INFO] 2025-06-26 11:37:41.332 DefaultAWSCredentialsProviderChain [139934512894016] Added EC2 metadata service credentials provider to the provider chain.
[INFO] 2025-06-26 11:37:41.332 Aws::Config::AWSProfileConfigLoader [139934512894016] Successfully reloaded configuration.
[WARN] 2025-06-26 11:37:41.332 ClientConfiguration [139934512894016] Retry Strategy will use the default max attempts.
[INFO] 2025-06-26 11:37:41.332 CurlHandleContainer [139934512894016] Initializing CurlHandleContainer with size 25
[INFO] 2025-06-26 11:37:41.332 AuthCredentialsProvider [139934512894016] (id=0x55b5d85c9890): TLS context not provided, initializing a new one for credentials provider.
[INFO] 2025-06-26 11:51:01.854 Aws_Init_Cleanup [140301607829568] Initiate AWS SDK for C++ with Version:1.9.234
[INFO] 2025-06-26 11:51:01.854 Aws::Config::AWSConfigFileProfileConfigLoader [140301607829568] Initializing config loader against fileName /home/<USER>/.aws/credentials and using profilePrefix = 0
[INFO] 2025-06-26 11:51:01.854 Aws::Config::AWSConfigFileProfileConfigLoader [140301607829568] Initializing config loader against fileName /home/<USER>/.aws/config and using profilePrefix = 1
[INFO] 2025-06-26 11:51:01.854 Aws::Config::AWSProfileConfigLoader [140301607829568] Successfully reloaded configuration.
[INFO] 2025-06-26 11:51:01.854 Aws::Config::AWSProfileConfigLoader [140301607829568] Successfully reloaded configuration.
[INFO] 2025-06-26 11:51:01.854 event-loop [140301607829568] id=0x5633a7adadd0: Initializing edge-triggered epoll
[INFO] 2025-06-26 11:51:01.854 event-loop [140301607829568] id=0x5633a7adadd0: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 11:51:01.854 event-loop [140301607829568] id=0x5633a7adadd0: Starting event-loop thread.
[INFO] 2025-06-26 11:51:01.854 event-loop [140301607829568] id=0x5633a7ad7630: Initializing edge-triggered epoll
[INFO] 2025-06-26 11:51:01.854 event-loop [140301607829568] id=0x5633a7ad7630: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 11:51:01.854 event-loop [140301607829568] id=0x5633a7ad7630: Starting event-loop thread.
[INFO] 2025-06-26 11:51:01.854 event-loop [140301607829568] id=0x5633a7ab0d70: Initializing edge-triggered epoll
[INFO] 2025-06-26 11:51:01.854 event-loop [140301607829568] id=0x5633a7ab0d70: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 11:51:01.854 event-loop [140301599422208] id=0x5633a7adadd0: main loop started
[INFO] 2025-06-26 11:51:01.854 event-loop [140301607829568] id=0x5633a7ab0d70: Starting event-loop thread.
[INFO] 2025-06-26 11:51:01.854 event-loop [140301599422208] id=0x5633a7adadd0: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 11:51:01.854 event-loop [140301607829568] id=0x5633a7aa4340: Initializing edge-triggered epoll
[INFO] 2025-06-26 11:51:01.854 event-loop [140301607829568] id=0x5633a7aa4340: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 11:51:01.854 event-loop [140301607829568] id=0x5633a7aa4340: Starting event-loop thread.
[INFO] 2025-06-26 11:51:01.854 event-loop [140301591029504] id=0x5633a7ad7630: main loop started
[INFO] 2025-06-26 11:51:01.854 event-loop [140301591029504] id=0x5633a7ad7630: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 11:51:01.854 event-loop [140301607829568] id=0x5633a7a9c050: Initializing edge-triggered epoll
[INFO] 2025-06-26 11:51:01.854 event-loop [140301607829568] id=0x5633a7a9c050: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 11:51:01.854 event-loop [140301607829568] id=0x5633a7a9c050: Starting event-loop thread.
[INFO] 2025-06-26 11:51:01.854 event-loop [140301582636800] id=0x5633a7ab0d70: main loop started
[INFO] 2025-06-26 11:51:01.854 event-loop [140301607829568] id=0x5633a7a8b540: Initializing edge-triggered epoll
[INFO] 2025-06-26 11:51:01.854 event-loop [140301582636800] id=0x5633a7ab0d70: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 11:51:01.854 event-loop [140301607829568] id=0x5633a7a8b540: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 11:51:01.854 event-loop [140301607829568] id=0x5633a7a8b540: Starting event-loop thread.
[INFO] 2025-06-26 11:51:01.854 event-loop [140301565851392] id=0x5633a7a9c050: main loop started
[INFO] 2025-06-26 11:51:01.854 event-loop [140301565851392] id=0x5633a7a9c050: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 11:51:01.854 event-loop [140301574244096] id=0x5633a7aa4340: main loop started
[INFO] 2025-06-26 11:51:01.854 dns [140301607829568] id=0x5633a7b568e0: Initializing default host resolver with 8 max host entries.
[INFO] 2025-06-26 11:51:01.854 event-loop [140301574244096] id=0x5633a7aa4340: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 11:51:01.854 channel-bootstrap [140301607829568] id=0x5633a7b56980: Initializing client bootstrap with event-loop group 0x5633a7ae5f80
[INFO] 2025-06-26 11:51:01.854 event-loop [140301079410432] id=0x5633a7a8b540: main loop started
[INFO] 2025-06-26 11:51:01.854 event-loop [140301079410432] id=0x5633a7a8b540: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 11:51:01.878 CurlHttpClient [140301607829568] Initializing Curl library with version: 7.86.0, ssl version: OpenSSL/3.1.2
[INFO] 2025-06-26 11:51:01.879 EC2MetadataClient [140301607829568] Using IMDS endpoint: http://***************
[WARN] 2025-06-26 11:51:01.879 ClientConfiguration [140301607829568] Retry Strategy will use the default max attempts.
[INFO] 2025-06-26 11:51:01.879 EC2MetadataClient [140301607829568] Creating AWSHttpResourceClient with max connections 2 and scheme http
[INFO] 2025-06-26 11:51:01.879 CurlHandleContainer [140301607829568] Initializing CurlHandleContainer with size 2
[WARN] 2025-06-26 11:51:01.879 ClientConfiguration [140301607829568] Retry Strategy will use the default max attempts.
[INFO] 2025-06-26 11:51:01.879 Aws::Config::AWSConfigFileProfileConfigLoader [140301607829568] Initializing config loader against fileName /home/<USER>/.aws/credentials and using profilePrefix = 0
[INFO] 2025-06-26 11:51:01.879 ProfileConfigFileAWSCredentialsProvider [140301607829568] Setting provider to read credentials from /home/<USER>/.aws/credentials for credentials file and /home/<USER>/.aws/config for the config file , for use with profile default
[INFO] 2025-06-26 11:51:01.879 ProcessCredentialsProvider [140301607829568] Setting process credentials provider to read config from default
[WARN] 2025-06-26 11:51:01.879 STSAssumeRoleWithWebIdentityCredentialsProvider [140301607829568] Token file must be specified to use STS AssumeRole web identity creds provider.
[INFO] 2025-06-26 11:51:01.879 SSOCredentialsProvider [140301607829568] Setting sso credentials provider to read config from default
[INFO] 2025-06-26 11:51:01.879 InstanceProfileCredentialsProvider [140301607829568] Creating Instance with default EC2MetadataClient and refresh rate 300000
[INFO] 2025-06-26 11:51:01.879 DefaultAWSCredentialsProviderChain [140301607829568] Added EC2 metadata service credentials provider to the provider chain.
[INFO] 2025-06-26 11:51:01.879 Aws::Config::AWSProfileConfigLoader [140301607829568] Successfully reloaded configuration.
[INFO] 2025-06-26 11:51:01.879 CurlHandleContainer [140301607829568] Initializing CurlHandleContainer with size 25
[INFO] 2025-06-26 11:51:01.879 CurlHandleContainer [140301347845888] Pool grown by 2
[INFO] 2025-06-26 11:51:01.879 CurlHandleContainer [140301347845888] Connection has been released. Continuing.
[INFO] 2025-06-26 11:51:01.889 CurlHandleContainer [140301339453184] Connection has been released. Continuing.
[INFO] 2025-06-26 11:51:01.899 CurlHandleContainer [140301331060480] Pool grown by 4
[INFO] 2025-06-26 11:51:01.899 CurlHandleContainer [140301331060480] Connection has been released. Continuing.
[INFO] 2025-06-26 11:51:01.909 CurlHandleContainer [140301322667776] Connection has been released. Continuing.
[WARN] 2025-06-26 11:51:02.408 AWSErrorMarshaller [140301331060480] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 11:51:02.408 AWSClient [140301331060480] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: C8G85GXN34GGCRMM
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 11:51:01 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : Xkh85fMwioo2HQ0CE9l8Dzg8mOgD2EstOUBhCuyPmhhXt6Bm3wvo3dGgSBGSCcuyHHjDDpW79wM=
x-amz-request-id : C8G85GXN34GGCRMM
[WARN] 2025-06-26 11:51:02.408 AWSClient [140301331060480] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 11:51:02.409 CurlHandleContainer [140301331060480] Connection has been released. Continuing.
[WARN] 2025-06-26 11:51:02.422 AWSErrorMarshaller [140301322667776] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 11:51:02.422 AWSClient [140301322667776] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: C8GF5T5CXY803K08
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 11:51:02 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : z+BebgRyjOH4N1lQfdo71SWuRculxr2RnksT+SIF7qjk3VDuibX0ODdN+EqJCtFvlzt0J4FrNzI=
x-amz-request-id : C8GF5T5CXY803K08
[WARN] 2025-06-26 11:51:02.422 AWSClient [140301322667776] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 11:51:02.423 CurlHandleContainer [140301322667776] Connection has been released. Continuing.
[WARN] 2025-06-26 11:51:02.431 AWSErrorMarshaller [140301339453184] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 11:51:02.431 AWSClient [140301339453184] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: C8GBM2QWW0RK0BGT
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 11:51:01 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : 3qpyieSjRrL/oIqpjESLgchUTEPeUHE6tWNpxsQGShjE0L33rmjty1SzA0pvGlteO241PbpEgHI=
x-amz-request-id : C8GBM2QWW0RK0BGT
[WARN] 2025-06-26 11:51:02.431 AWSClient [140301339453184] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 11:51:02.432 CurlHandleContainer [140301339453184] Connection has been released. Continuing.
[WARN] 2025-06-26 11:51:02.453 AWSErrorMarshaller [140301347845888] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 11:51:02.453 AWSClient [140301347845888] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: C8G68YG519N357E8
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 11:51:01 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : nc0AhCxYjntUJKB+oBq5l5aD8H3/4MUlrIfKGq2QXSNlUdOuvsZJYWAw+vrkfOFD4DmVLQBrlKU=
x-amz-request-id : C8G68YG519N357E8
[WARN] 2025-06-26 11:51:02.453 AWSClient [140301347845888] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 11:51:02.453 CurlHandleContainer [140301347845888] Connection has been released. Continuing.
[WARN] 2025-06-26 11:51:02.553 AWSErrorMarshaller [140301331060480] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 11:51:02.553 AWSClient [140301331060480] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: C8G2YVABM0AQ8Q6D
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 11:51:02 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : drVkmzTnPvBDy6MjtSvj7wEnsLHJoWePMVeoPnqn5/zQPxLjE8EANnR+5+YdfF+YpArAjF8GMfI=
x-amz-request-id : C8G2YVABM0AQ8Q6D
[WARN] 2025-06-26 11:51:02.553 AWSClient [140301331060480] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 11:51:02.553 CurlHandleContainer [140301331060480] Connection has been released. Continuing.
[WARN] 2025-06-26 11:51:02.578 AWSErrorMarshaller [140301322667776] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 11:51:02.578 AWSClient [140301322667776] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: C8GBD2G13TFP2MG8
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 11:51:02 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : EMspVlSR/7uUVOCTAn09ZbUtwfRARoGP2Z4c12WaQYojfAD2eLjvreCTRof4KsmulZWW7bKflo8=
x-amz-request-id : C8GBD2G13TFP2MG8
[WARN] 2025-06-26 11:51:02.578 AWSClient [140301322667776] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 11:51:02.579 CurlHandleContainer [140301322667776] Connection has been released. Continuing.
[WARN] 2025-06-26 11:51:02.588 AWSErrorMarshaller [140301339453184] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 11:51:02.588 AWSClient [140301339453184] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: C8G75RRTMZ0S9DW6
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 11:51:01 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : wHZmiGhYGFlo9gyzfziFJHtdKWQ7EBqF38us9MqjCZSfQxAZsMxfGyosj+jAFV0ChD+rJzNDUa4=
x-amz-request-id : C8G75RRTMZ0S9DW6
[WARN] 2025-06-26 11:51:02.588 AWSClient [140301339453184] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[WARN] 2025-06-26 11:51:02.616 AWSErrorMarshaller [140301347845888] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 11:51:02.616 AWSClient [140301347845888] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: C8GD8Z3EVANMWC54
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 11:51:01 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : xMDnyBSvOarhaTg5i8pGEUBK3dqUPgnOGco6x3UfbeeBK1SoVysCTJLFk5aQLsN+H6oZZGLqINA=
x-amz-request-id : C8GD8Z3EVANMWC54
[WARN] 2025-06-26 11:51:02.616 AWSClient [140301347845888] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[WARN] 2025-06-26 11:51:02.697 AWSErrorMarshaller [140301331060480] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 11:51:02.697 AWSClient [140301331060480] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: C8GA4T5REEQGNFSV
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 11:51:02 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : KCXbJc8Ruq7+YaAIAVAdgU8zxbIB6J7B7/OgIV2XxOX/iXMGjbSR2zDkptN6IeT8kEeT1qTaJfc=
x-amz-request-id : C8GA4T5REEQGNFSV
[WARN] 2025-06-26 11:51:02.697 AWSClient [140301331060480] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[WARN] 2025-06-26 11:51:02.730 AWSErrorMarshaller [140301322667776] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 11:51:02.730 AWSClient [140301322667776] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: C8G8Q2KVKPPY0SCS
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 11:51:02 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : c2lyRqR5ul/7ht5AzxKL+DwqGaqM6kbKKZa1EFrcaLDOH+bKSavd2cLCEbdpVEboYPaa19fq3Fg=
x-amz-request-id : C8G8Q2KVKPPY0SCS
[WARN] 2025-06-26 11:51:02.730 AWSClient [140301322667776] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 11:51:02.730 CurlHandleContainer [140301607829568] Cleaning up CurlHandleContainer.
[INFO] 2025-06-26 11:51:04.737 Aws::Config::AWSConfigFileProfileConfigLoader [140301607829568] Initializing config loader against fileName /home/<USER>/.aws/credentials and using profilePrefix = 0
[INFO] 2025-06-26 11:51:04.737 ProfileConfigFileAWSCredentialsProvider [140301607829568] Setting provider to read credentials from /home/<USER>/.aws/credentials for credentials file and /home/<USER>/.aws/config for the config file , for use with profile default
[INFO] 2025-06-26 11:51:04.737 ProcessCredentialsProvider [140301607829568] Setting process credentials provider to read config from default
[WARN] 2025-06-26 11:51:04.737 STSAssumeRoleWithWebIdentityCredentialsProvider [140301607829568] Token file must be specified to use STS AssumeRole web identity creds provider.
[INFO] 2025-06-26 11:51:04.737 SSOCredentialsProvider [140301607829568] Setting sso credentials provider to read config from default
[INFO] 2025-06-26 11:51:04.737 InstanceProfileCredentialsProvider [140301607829568] Creating Instance with default EC2MetadataClient and refresh rate 300000
[INFO] 2025-06-26 11:51:04.737 DefaultAWSCredentialsProviderChain [140301607829568] Added EC2 metadata service credentials provider to the provider chain.
[INFO] 2025-06-26 11:51:04.737 Aws::Config::AWSProfileConfigLoader [140301607829568] Successfully reloaded configuration.
[WARN] 2025-06-26 11:51:04.738 ClientConfiguration [140301607829568] Retry Strategy will use the default max attempts.
[INFO] 2025-06-26 11:51:04.738 CurlHandleContainer [140301607829568] Initializing CurlHandleContainer with size 25
