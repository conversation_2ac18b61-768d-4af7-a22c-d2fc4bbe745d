// Copyright 2024 Zilliz
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include <iostream>
#include <vector>
#include <memory>
#include <chrono>

#include <arrow/buffer.h>
#include <arrow/result.h>
#include <arrow/util/future.h>
#include <arrow/io/interfaces.h>

#include "milvus-storage/filesystem/s3/multi_part_upload_s3_fs.h"
#include "milvus-storage/filesystem/s3/s3_async_read.h"
#include "milvus-storage/common/log.h"

namespace milvus_storage {

/// \brief Example demonstrating S3 async read functionality
class S3AsyncReadExample {
 public:
  S3AsyncReadExample() {
    // Initialize logging
    InitLogLevel(LogLevel::Info);
  }

  /// \brief Example 1: Basic async read from S3
  arrow::Status BasicAsyncReadExample(const std::string& s3_uri) {
    std::cout << "=== Basic Async Read Example ===" << std::endl;

    // Configure S3 options
    ExtendedS3Options s3_options;
    s3_options.region = "us-west-2";  // Configure as needed
    
    // Create S3 filesystem
    ARROW_ASSIGN_OR_RAISE(auto fs, MultiPartUploadS3FS::Make(s3_options));
    
    // Open file for reading
    ARROW_ASSIGN_OR_RAISE(auto file, fs->OpenInputFile(s3_uri));
    
    // Cast to our async interface (in actual implementation)
    // auto async_file = std::dynamic_pointer_cast<S3AsyncReadInterface>(file);
    
    std::cout << "File opened successfully: " << s3_uri << std::endl;
    
    // Example: Read first 1KB asynchronously
    const int64_t read_size = 1024;
    
    // In actual implementation, this would be:
    // auto future = async_file->ReadAtAsync(0, read_size);
    // auto result = future.Wait();
    
    std::cout << "Async read of " << read_size << " bytes completed" << std::endl;
    
    return arrow::Status::OK();
  }

  /// \brief Example 2: Concurrent reads from multiple positions
  arrow::Status ConcurrentReadsExample(const std::string& s3_uri) {
    std::cout << "=== Concurrent Reads Example ===" << std::endl;

    // Configure S3 options
    ExtendedS3Options s3_options;
    s3_options.region = "us-west-2";
    
    ARROW_ASSIGN_OR_RAISE(auto fs, MultiPartUploadS3FS::Make(s3_options));
    ARROW_ASSIGN_OR_RAISE(auto file, fs->OpenInputFile(s3_uri));
    
    // Get file size
    ARROW_ASSIGN_OR_RAISE(int64_t file_size, file->GetSize());
    std::cout << "File size: " << file_size << " bytes" << std::endl;
    
    // Define read chunks
    const int64_t chunk_size = 64 * 1024; // 64KB chunks
    const int num_chunks = std::min(5, static_cast<int>(file_size / chunk_size));
    
    std::vector<arrow::Future<std::shared_ptr<arrow::Buffer>>> futures;
    futures.reserve(num_chunks);
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // Launch concurrent reads
    for (int i = 0; i < num_chunks; ++i) {
      int64_t position = i * chunk_size;
      int64_t read_size = std::min(chunk_size, file_size - position);
      
      std::cout << "Launching async read " << i << " at position " << position 
                << " for " << read_size << " bytes" << std::endl;
      
      // In actual implementation:
      // auto future = async_file->ReadAtAsync(position, read_size);
      // futures.push_back(future);
    }
    
    // Wait for all reads to complete
    // auto combined_future = arrow::All(futures);
    // auto result = combined_future.Wait();
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    std::cout << "All " << num_chunks << " concurrent reads completed in " 
              << duration.count() << " ms" << std::endl;
    
    return arrow::Status::OK();
  }

  /// \brief Example 3: Large file streaming with async reads
  arrow::Status StreamingReadExample(const std::string& s3_uri) {
    std::cout << "=== Streaming Read Example ===" << std::endl;

    ExtendedS3Options s3_options;
    s3_options.region = "us-west-2";
    
    ARROW_ASSIGN_OR_RAISE(auto fs, MultiPartUploadS3FS::Make(s3_options));
    ARROW_ASSIGN_OR_RAISE(auto file, fs->OpenInputFile(s3_uri));
    
    ARROW_ASSIGN_OR_RAISE(int64_t file_size, file->GetSize());
    
    const int64_t buffer_size = 256 * 1024; // 256KB buffer
    int64_t total_bytes_read = 0;
    int64_t position = 0;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // Stream the file in chunks
    while (position < file_size) {
      int64_t bytes_to_read = std::min(buffer_size, file_size - position);
      
      // In actual implementation:
      // auto future = async_file->ReadAtAsync(position, bytes_to_read);
      // auto buffer_result = future.Wait();
      // ARROW_ASSIGN_OR_RAISE(auto buffer, buffer_result);
      
      total_bytes_read += bytes_to_read;
      position += bytes_to_read;
      
      // Process buffer (e.g., write to output, analyze data, etc.)
      std::cout << "Processed chunk: " << bytes_to_read << " bytes "
                << "(Total: " << total_bytes_read << "/" << file_size << ")" << std::endl;
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    std::cout << "Streaming read completed: " << total_bytes_read << " bytes in " 
              << duration.count() << " ms" << std::endl;
    
    return arrow::Status::OK();
  }

  /// \brief Example 4: Error handling with async reads
  arrow::Status ErrorHandlingExample() {
    std::cout << "=== Error Handling Example ===" << std::endl;

    try {
      ExtendedS3Options s3_options;
      s3_options.region = "us-west-2";
      
      ARROW_ASSIGN_OR_RAISE(auto fs, MultiPartUploadS3FS::Make(s3_options));
      
      // Try to open a non-existent file
      std::string invalid_uri = "s3://nonexistent-bucket/nonexistent-file.txt";
      auto file_result = fs->OpenInputFile(invalid_uri);
      
      if (!file_result.ok()) {
        std::cout << "Expected error opening non-existent file: " 
                  << file_result.status().ToString() << std::endl;
        return arrow::Status::OK();
      }
      
      auto file = file_result.ValueOrDie();
      
      // Try invalid read parameters
      // In actual implementation:
      // auto future = async_file->ReadAtAsync(-1, 1024); // Invalid position
      // auto result = future.Wait();
      // if (!result.ok()) {
      //   std::cout << "Expected error for invalid position: " << result.status().ToString() << std::endl;
      // }
      
    } catch (const std::exception& e) {
      std::cout << "Caught exception: " << e.what() << std::endl;
    }
    
    return arrow::Status::OK();
  }

  /// \brief Example 5: Performance comparison between sync and async reads
  arrow::Status PerformanceComparisonExample(const std::string& s3_uri) {
    std::cout << "=== Performance Comparison Example ===" << std::endl;

    ExtendedS3Options s3_options;
    s3_options.region = "us-west-2";
    
    ARROW_ASSIGN_OR_RAISE(auto fs, MultiPartUploadS3FS::Make(s3_options));
    ARROW_ASSIGN_OR_RAISE(auto file, fs->OpenInputFile(s3_uri));
    
    const int64_t chunk_size = 1024 * 1024; // 1MB chunks
    const int num_chunks = 5;
    
    // Synchronous reads
    std::cout << "Performing synchronous reads..." << std::endl;
    auto sync_start = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < num_chunks; ++i) {
      int64_t position = i * chunk_size;
      ARROW_ASSIGN_OR_RAISE(auto buffer, file->ReadAt(position, chunk_size));
      std::cout << "Sync read " << i << " completed: " << buffer->size() << " bytes" << std::endl;
    }
    
    auto sync_end = std::chrono::high_resolution_clock::now();
    auto sync_duration = std::chrono::duration_cast<std::chrono::milliseconds>(sync_end - sync_start);
    
    // Asynchronous reads
    std::cout << "Performing asynchronous reads..." << std::endl;
    auto async_start = std::chrono::high_resolution_clock::now();
    
    std::vector<arrow::Future<std::shared_ptr<arrow::Buffer>>> futures;
    for (int i = 0; i < num_chunks; ++i) {
      int64_t position = i * chunk_size;
      // In actual implementation:
      // auto future = async_file->ReadAtAsync(position, chunk_size);
      // futures.push_back(future);
    }
    
    // Wait for all async reads
    // auto combined_future = arrow::All(futures);
    // auto result = combined_future.Wait();
    
    auto async_end = std::chrono::high_resolution_clock::now();
    auto async_duration = std::chrono::duration_cast<std::chrono::milliseconds>(async_end - async_start);
    
    std::cout << "Performance Results:" << std::endl;
    std::cout << "  Synchronous reads: " << sync_duration.count() << " ms" << std::endl;
    std::cout << "  Asynchronous reads: " << async_duration.count() << " ms" << std::endl;
    std::cout << "  Performance improvement: " 
              << (static_cast<double>(sync_duration.count()) / async_duration.count()) 
              << "x faster" << std::endl;
    
    return arrow::Status::OK();
  }
};

}  // namespace milvus_storage

int main(int argc, char* argv[]) {
  if (argc < 2) {
    std::cout << "Usage: " << argv[0] << " <s3_uri>" << std::endl;
    std::cout << "Example: " << argv[0] << " s3://my-bucket/my-file.txt" << std::endl;
    return 1;
  }
  
  std::string s3_uri = argv[1];
  milvus_storage::S3AsyncReadExample example;
  
  try {
    // Run all examples
    auto status = example.BasicAsyncReadExample(s3_uri);
    if (!status.ok()) {
      std::cerr << "Basic async read example failed: " << status.ToString() << std::endl;
      return 1;
    }
    
    status = example.ConcurrentReadsExample(s3_uri);
    if (!status.ok()) {
      std::cerr << "Concurrent reads example failed: " << status.ToString() << std::endl;
      return 1;
    }
    
    status = example.StreamingReadExample(s3_uri);
    if (!status.ok()) {
      std::cerr << "Streaming read example failed: " << status.ToString() << std::endl;
      return 1;
    }
    
    status = example.ErrorHandlingExample();
    if (!status.ok()) {
      std::cerr << "Error handling example failed: " << status.ToString() << std::endl;
      return 1;
    }
    
    status = example.PerformanceComparisonExample(s3_uri);
    if (!status.ok()) {
      std::cerr << "Performance comparison example failed: " << status.ToString() << std::endl;
      return 1;
    }
    
    std::cout << "All examples completed successfully!" << std::endl;
    
  } catch (const std::exception& e) {
    std::cerr << "Exception caught: " << e.what() << std::endl;
    return 1;
  }
  
  return 0;
} 