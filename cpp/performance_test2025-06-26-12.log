[INFO] 2025-06-26 12:02:26.237 Aws_Init_Cleanup [140321394593856] Initiate AWS SDK for C++ with Version:1.9.234
[INFO] 2025-06-26 12:02:26.237 Aws::Config::AWSConfigFileProfileConfigLoader [140321394593856] Initializing config loader against fileName /home/<USER>/.aws/credentials and using profilePrefix = 0
[INFO] 2025-06-26 12:02:26.237 Aws::Config::AWSConfigFileProfileConfigLoader [140321394593856] Initializing config loader against fileName /home/<USER>/.aws/config and using profilePrefix = 1
[INFO] 2025-06-26 12:02:26.237 Aws::Config::AWSProfileConfigLoader [140321394593856] Successfully reloaded configuration.
[INFO] 2025-06-26 12:02:26.237 Aws::Config::AWSProfileConfigLoader [140321394593856] Successfully reloaded configuration.
[INFO] 2025-06-26 12:02:26.237 event-loop [140321394593856] id=0x562e905817b0: Initializing edge-triggered epoll
[INFO] 2025-06-26 12:02:26.237 event-loop [140321394593856] id=0x562e905817b0: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 12:02:26.237 event-loop [140321394593856] id=0x562e905817b0: Starting event-loop thread.
[INFO] 2025-06-26 12:02:26.237 event-loop [140321394593856] id=0x562e905a2630: Initializing edge-triggered epoll
[INFO] 2025-06-26 12:02:26.237 event-loop [140321394593856] id=0x562e905a2630: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 12:02:26.237 event-loop [140321394593856] id=0x562e905a2630: Starting event-loop thread.
[INFO] 2025-06-26 12:02:26.237 event-loop [140321394593856] id=0x562e9057bd70: Initializing edge-triggered epoll
[INFO] 2025-06-26 12:02:26.237 event-loop [140321394593856] id=0x562e9057bd70: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 12:02:26.237 event-loop [140321394593856] id=0x562e9057bd70: Starting event-loop thread.
[INFO] 2025-06-26 12:02:26.237 event-loop [140321394593856] id=0x562e9056f340: Initializing edge-triggered epoll
[INFO] 2025-06-26 12:02:26.237 event-loop [140321386186496] id=0x562e905817b0: main loop started
[INFO] 2025-06-26 12:02:26.237 event-loop [140321394593856] id=0x562e9056f340: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 12:02:26.237 event-loop [140321394593856] id=0x562e9056f340: Starting event-loop thread.
[INFO] 2025-06-26 12:02:26.237 event-loop [140321386186496] id=0x562e905817b0: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 12:02:26.237 event-loop [140321377793792] id=0x562e905a2630: main loop started
[INFO] 2025-06-26 12:02:26.237 event-loop [140321377793792] id=0x562e905a2630: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 12:02:26.237 event-loop [140321394593856] id=0x562e90567050: Initializing edge-triggered epoll
[INFO] 2025-06-26 12:02:26.237 event-loop [140321394593856] id=0x562e90567050: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 12:02:26.237 event-loop [140321394593856] id=0x562e90567050: Starting event-loop thread.
[INFO] 2025-06-26 12:02:26.237 event-loop [140321369401088] id=0x562e9057bd70: main loop started
[INFO] 2025-06-26 12:02:26.237 event-loop [140321369401088] id=0x562e9057bd70: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 12:02:26.237 event-loop [140321394593856] id=0x562e90556540: Initializing edge-triggered epoll
[INFO] 2025-06-26 12:02:26.237 event-loop [140321394593856] id=0x562e90556540: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 12:02:26.237 event-loop [140321394593856] id=0x562e90556540: Starting event-loop thread.
[INFO] 2025-06-26 12:02:26.237 event-loop [140321361008384] id=0x562e9056f340: main loop started
[INFO] 2025-06-26 12:02:26.237 event-loop [140321361008384] id=0x562e9056f340: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 12:02:26.237 dns [140321394593856] id=0x562e906218e0: Initializing default host resolver with 8 max host entries.
[INFO] 2025-06-26 12:02:26.237 channel-bootstrap [140321394593856] id=0x562e90621980: Initializing client bootstrap with event-loop group 0x562e905b0f80
[INFO] 2025-06-26 12:02:26.237 event-loop [140321144960768] id=0x562e90567050: main loop started
[INFO] 2025-06-26 12:02:26.237 event-loop [140321144960768] id=0x562e90567050: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 12:02:26.237 event-loop [140321136568064] id=0x562e90556540: main loop started
[INFO] 2025-06-26 12:02:26.237 event-loop [140321136568064] id=0x562e90556540: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 12:02:26.260 CurlHttpClient [140321394593856] Initializing Curl library with version: 7.86.0, ssl version: OpenSSL/3.1.2
[INFO] 2025-06-26 12:02:26.260 EC2MetadataClient [140321394593856] Using IMDS endpoint: http://***************
[WARN] 2025-06-26 12:02:26.260 ClientConfiguration [140321394593856] Retry Strategy will use the default max attempts.
[INFO] 2025-06-26 12:02:26.260 EC2MetadataClient [140321394593856] Creating AWSHttpResourceClient with max connections 2 and scheme http
[INFO] 2025-06-26 12:02:26.260 CurlHandleContainer [140321394593856] Initializing CurlHandleContainer with size 2
[WARN] 2025-06-26 12:02:26.260 ClientConfiguration [140321394593856] Retry Strategy will use the default max attempts.
[INFO] 2025-06-26 12:02:26.261 Aws::Config::AWSConfigFileProfileConfigLoader [140321394593856] Initializing config loader against fileName /home/<USER>/.aws/credentials and using profilePrefix = 0
[INFO] 2025-06-26 12:02:26.261 ProfileConfigFileAWSCredentialsProvider [140321394593856] Setting provider to read credentials from /home/<USER>/.aws/credentials for credentials file and /home/<USER>/.aws/config for the config file , for use with profile default
[INFO] 2025-06-26 12:02:26.261 ProcessCredentialsProvider [140321394593856] Setting process credentials provider to read config from default
[WARN] 2025-06-26 12:02:26.261 STSAssumeRoleWithWebIdentityCredentialsProvider [140321394593856] Token file must be specified to use STS AssumeRole web identity creds provider.
[INFO] 2025-06-26 12:02:26.261 SSOCredentialsProvider [140321394593856] Setting sso credentials provider to read config from default
[INFO] 2025-06-26 12:02:26.261 InstanceProfileCredentialsProvider [140321394593856] Creating Instance with default EC2MetadataClient and refresh rate 300000
[INFO] 2025-06-26 12:02:26.261 DefaultAWSCredentialsProviderChain [140321394593856] Added EC2 metadata service credentials provider to the provider chain.
[INFO] 2025-06-26 12:02:26.261 Aws::Config::AWSProfileConfigLoader [140321394593856] Successfully reloaded configuration.
[INFO] 2025-06-26 12:02:26.261 CurlHandleContainer [140321394593856] Initializing CurlHandleContainer with size 25
[INFO] 2025-06-26 12:02:26.261 CurlHandleContainer [140321128175360] Pool grown by 2
[INFO] 2025-06-26 12:02:26.261 CurlHandleContainer [140321128175360] Connection has been released. Continuing.
[INFO] 2025-06-26 12:02:26.271 CurlHandleContainer [140321119782656] Connection has been released. Continuing.
[INFO] 2025-06-26 12:02:26.281 CurlHandleContainer [140321111389952] Pool grown by 4
[INFO] 2025-06-26 12:02:26.281 CurlHandleContainer [140321111389952] Connection has been released. Continuing.
[INFO] 2025-06-26 12:02:26.291 CurlHandleContainer [140321102997248] Connection has been released. Continuing.
[WARN] 2025-06-26 12:02:26.739 AWSErrorMarshaller [140321111389952] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:02:26.739 AWSClient [140321111389952] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: 3ZG41RE3PCT7P53R
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:02:26 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : T7JJW6rYum24n+3UGoDQzwUEo1RzbW4+YwNX8J4YRd5PmlTUvF7dbG9e3c//pOG2oHPlCGA0I68=
x-amz-request-id : 3ZG41RE3PCT7P53R
[WARN] 2025-06-26 12:02:26.739 AWSClient [140321111389952] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 12:02:26.739 CurlHandleContainer [140321111389952] Connection has been released. Continuing.
[WARN] 2025-06-26 12:02:26.748 AWSErrorMarshaller [140321119782656] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:02:26.748 AWSClient [140321119782656] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: 3ZGDKN822S00PKVP
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:02:26 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : u9ghPdFTMesZ0CMoX0V5VikA2lkEwcz2AMZEJouKyGkHEBKF/UOISUqI5IUYAWqqRiMOUfCLx04=
x-amz-request-id : 3ZGDKN822S00PKVP
[WARN] 2025-06-26 12:02:26.748 AWSClient [140321119782656] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 12:02:26.749 CurlHandleContainer [140321119782656] Connection has been released. Continuing.
[WARN] 2025-06-26 12:02:26.779 AWSErrorMarshaller [140321128175360] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:02:26.779 AWSClient [140321128175360] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: 3ZG7C8D265TPDTQW
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:02:26 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : ZVFr7v/V37ChaJMiBa4Mstaqyz50GOjm3O4srfVdcN4B+nS2zMgyY8r0hmw3Yuk77Tp2Ljd7ISg=
x-amz-request-id : 3ZG7C8D265TPDTQW
[WARN] 2025-06-26 12:02:26.779 AWSClient [140321128175360] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 12:02:26.780 CurlHandleContainer [140321128175360] Connection has been released. Continuing.
[WARN] 2025-06-26 12:02:26.803 AWSErrorMarshaller [140321102997248] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:02:26.803 AWSClient [140321102997248] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: 3ZG3TNCC0Q4Q347A
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:02:26 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : fvlflQ1hUol+8nsRsQ1HRHlx5LLZguCjQI8RV0ivr2xzg9cHNkKznuhW38TbrB9QTs6zyBPs9uY=
x-amz-request-id : 3ZG3TNCC0Q4Q347A
[WARN] 2025-06-26 12:02:26.803 AWSClient [140321102997248] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 12:02:26.803 CurlHandleContainer [140321102997248] Connection has been released. Continuing.
[WARN] 2025-06-26 12:02:26.884 AWSErrorMarshaller [140321111389952] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:02:26.884 AWSClient [140321111389952] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: 3ZG6TZXBKJK6EENG
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:02:26 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : kTkbvgQnHKceJA6y4WgnND9vpP0E2DhXjoQSaXfi8Hlk4J1zmj3s4ISE7ZTeouIFLVp1O/3vY3w=
x-amz-request-id : 3ZG6TZXBKJK6EENG
[WARN] 2025-06-26 12:02:26.884 AWSClient [140321111389952] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 12:02:26.884 CurlHandleContainer [140321111389952] Connection has been released. Continuing.
[WARN] 2025-06-26 12:02:26.902 AWSErrorMarshaller [140321119782656] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:02:26.902 AWSClient [140321119782656] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: 3ZGDNDTFGSSBTPCV
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:02:26 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : yriM0WUvWn8Rep0mNCkcCl+y1UpPqX9jiHmD2OVEyQ/sKJ6r3PYRVSg9UNXQak/nSmWFLPxRzfI=
x-amz-request-id : 3ZGDNDTFGSSBTPCV
[WARN] 2025-06-26 12:02:26.902 AWSClient [140321119782656] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 12:02:26.903 CurlHandleContainer [140321119782656] Connection has been released. Continuing.
[WARN] 2025-06-26 12:02:26.946 AWSErrorMarshaller [140321128175360] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:02:26.946 AWSClient [140321128175360] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: 3ZG83W1DR4RTBFE2
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:02:26 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : o8i42Se9I6X5qsQuNDd9rLkcqF1wc5ZwHYUZ0Lmm9oLKgeB0pw2c/0Okl4vKpPVqu0ofIJVFdJA=
x-amz-request-id : 3ZG83W1DR4RTBFE2
[WARN] 2025-06-26 12:02:26.946 AWSClient [140321128175360] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[WARN] 2025-06-26 12:02:26.968 AWSErrorMarshaller [140321102997248] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:02:26.968 AWSClient [140321102997248] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: 3ZGER95M7TT1BGS1
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:02:26 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : lo8weo8xUkbk7P3uDT7cMqbBu6yX1MZ3HXfw6RWSnQk2l69BKOr6UIl2vY3aSv0LDUNaXUKUwxQ=
x-amz-request-id : 3ZGER95M7TT1BGS1
[WARN] 2025-06-26 12:02:26.968 AWSClient [140321102997248] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[WARN] 2025-06-26 12:02:27.028 AWSErrorMarshaller [140321111389952] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:02:27.028 AWSClient [140321111389952] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: 3ZG8EECA65703N7R
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:02:26 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : yL4CAvGrvxOWZbcpiayN3LK+gGT2Ok5fHTL2y8r5E3DeYI7NA/zQLlFy55ryi+aY9lxCow/uJ3I=
x-amz-request-id : 3ZG8EECA65703N7R
[WARN] 2025-06-26 12:02:27.028 AWSClient [140321111389952] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[WARN] 2025-06-26 12:02:27.056 AWSErrorMarshaller [140321119782656] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:02:27.056 AWSClient [140321119782656] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: 3ZG6G4YK09850KNC
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:02:26 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : 62tOQMMVT1gCV+9d/j7lA/1NoaCJPVLGCgKzbIYFHrpQGv3C6hDOVFFPKu45hS5gJdqjE1Ey9kg=
x-amz-request-id : 3ZG6G4YK09850KNC
[WARN] 2025-06-26 12:02:27.056 AWSClient [140321119782656] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 12:02:27.056 CurlHandleContainer [140321394593856] Cleaning up CurlHandleContainer.
[INFO] 2025-06-26 12:02:29.066 Aws::Config::AWSConfigFileProfileConfigLoader [140321394593856] Initializing config loader against fileName /home/<USER>/.aws/credentials and using profilePrefix = 0
[INFO] 2025-06-26 12:02:29.066 ProfileConfigFileAWSCredentialsProvider [140321394593856] Setting provider to read credentials from /home/<USER>/.aws/credentials for credentials file and /home/<USER>/.aws/config for the config file , for use with profile default
[INFO] 2025-06-26 12:02:29.066 ProcessCredentialsProvider [140321394593856] Setting process credentials provider to read config from default
[WARN] 2025-06-26 12:02:29.066 STSAssumeRoleWithWebIdentityCredentialsProvider [140321394593856] Token file must be specified to use STS AssumeRole web identity creds provider.
[INFO] 2025-06-26 12:02:29.066 SSOCredentialsProvider [140321394593856] Setting sso credentials provider to read config from default
[INFO] 2025-06-26 12:02:29.066 InstanceProfileCredentialsProvider [140321394593856] Creating Instance with default EC2MetadataClient and refresh rate 300000
[INFO] 2025-06-26 12:02:29.066 DefaultAWSCredentialsProviderChain [140321394593856] Added EC2 metadata service credentials provider to the provider chain.
[INFO] 2025-06-26 12:02:29.066 Aws::Config::AWSProfileConfigLoader [140321394593856] Successfully reloaded configuration.
[WARN] 2025-06-26 12:02:29.067 ClientConfiguration [140321394593856] Retry Strategy will use the default max attempts.
[INFO] 2025-06-26 12:02:29.067 CurlHandleContainer [140321394593856] Initializing CurlHandleContainer with size 25
[INFO] 2025-06-26 12:02:29.067 AuthCredentialsProvider [140321394593856] (id=0x562e907d0f70): TLS context not provided, initializing a new one for credentials provider.
[INFO] 2025-06-26 12:04:18.542 Aws_Init_Cleanup [140477567127616] Initiate AWS SDK for C++ with Version:1.9.234
[INFO] 2025-06-26 12:04:18.542 Aws::Config::AWSConfigFileProfileConfigLoader [140477567127616] Initializing config loader against fileName /home/<USER>/.aws/credentials and using profilePrefix = 0
[INFO] 2025-06-26 12:04:18.542 Aws::Config::AWSConfigFileProfileConfigLoader [140477567127616] Initializing config loader against fileName /home/<USER>/.aws/config and using profilePrefix = 1
[INFO] 2025-06-26 12:04:18.542 Aws::Config::AWSProfileConfigLoader [140477567127616] Successfully reloaded configuration.
[INFO] 2025-06-26 12:04:18.542 Aws::Config::AWSProfileConfigLoader [140477567127616] Successfully reloaded configuration.
[INFO] 2025-06-26 12:04:18.542 event-loop [140477567127616] id=0x561a911e87b0: Initializing edge-triggered epoll
[INFO] 2025-06-26 12:04:18.542 event-loop [140477567127616] id=0x561a911e87b0: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 12:04:18.542 event-loop [140477567127616] id=0x561a911e87b0: Starting event-loop thread.
[INFO] 2025-06-26 12:04:18.542 event-loop [140477567127616] id=0x561a91209630: Initializing edge-triggered epoll
[INFO] 2025-06-26 12:04:18.542 event-loop [140477567127616] id=0x561a91209630: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 12:04:18.542 event-loop [140477567127616] id=0x561a91209630: Starting event-loop thread.
[INFO] 2025-06-26 12:04:18.542 event-loop [140477567127616] id=0x561a911e2d70: Initializing edge-triggered epoll
[INFO] 2025-06-26 12:04:18.542 event-loop [140477567127616] id=0x561a911e2d70: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 12:04:18.542 event-loop [140477567127616] id=0x561a911e2d70: Starting event-loop thread.
[INFO] 2025-06-26 12:04:18.542 event-loop [140477550327552] id=0x561a91209630: main loop started
[INFO] 2025-06-26 12:04:18.542 event-loop [140477558720256] id=0x561a911e87b0: main loop started
[INFO] 2025-06-26 12:04:18.542 event-loop [140477550327552] id=0x561a91209630: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 12:04:18.542 event-loop [140477558720256] id=0x561a911e87b0: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 12:04:18.542 event-loop [140477567127616] id=0x561a911d6340: Initializing edge-triggered epoll
[INFO] 2025-06-26 12:04:18.542 event-loop [140477567127616] id=0x561a911d6340: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 12:04:18.542 event-loop [140477567127616] id=0x561a911d6340: Starting event-loop thread.
[INFO] 2025-06-26 12:04:18.542 event-loop [140477567127616] id=0x561a911ce050: Initializing edge-triggered epoll
[INFO] 2025-06-26 12:04:18.542 event-loop [140477567127616] id=0x561a911ce050: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 12:04:18.542 event-loop [140477567127616] id=0x561a911ce050: Starting event-loop thread.
[INFO] 2025-06-26 12:04:18.542 event-loop [140477541934848] id=0x561a911e2d70: main loop started
[INFO] 2025-06-26 12:04:18.542 event-loop [140477567127616] id=0x561a911bd540: Initializing edge-triggered epoll
[INFO] 2025-06-26 12:04:18.542 event-loop [140477541934848] id=0x561a911e2d70: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 12:04:18.542 event-loop [140477567127616] id=0x561a911bd540: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 12:04:18.542 event-loop [140477567127616] id=0x561a911bd540: Starting event-loop thread.
[INFO] 2025-06-26 12:04:18.542 event-loop [140477533542144] id=0x561a911d6340: main loop started
[INFO] 2025-06-26 12:04:18.542 event-loop [140477533542144] id=0x561a911d6340: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 12:04:18.542 dns [140477567127616] id=0x561a912888e0: Initializing default host resolver with 8 max host entries.
[INFO] 2025-06-26 12:04:18.542 channel-bootstrap [140477567127616] id=0x561a91288980: Initializing client bootstrap with event-loop group 0x561a91217f80
[INFO] 2025-06-26 12:04:18.542 event-loop [140477525149440] id=0x561a911ce050: main loop started
[INFO] 2025-06-26 12:04:18.542 event-loop [140477525149440] id=0x561a911ce050: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 12:04:18.542 event-loop [140477173069568] id=0x561a911bd540: main loop started
[INFO] 2025-06-26 12:04:18.542 event-loop [140477173069568] id=0x561a911bd540: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 12:04:18.566 CurlHttpClient [140477567127616] Initializing Curl library with version: 7.86.0, ssl version: OpenSSL/3.1.2
[INFO] 2025-06-26 12:04:18.566 EC2MetadataClient [140477567127616] Using IMDS endpoint: http://***************
[WARN] 2025-06-26 12:04:18.566 ClientConfiguration [140477567127616] Retry Strategy will use the default max attempts.
[INFO] 2025-06-26 12:04:18.566 EC2MetadataClient [140477567127616] Creating AWSHttpResourceClient with max connections 2 and scheme http
[INFO] 2025-06-26 12:04:18.566 CurlHandleContainer [140477567127616] Initializing CurlHandleContainer with size 2
[WARN] 2025-06-26 12:04:18.566 ClientConfiguration [140477567127616] Retry Strategy will use the default max attempts.
[INFO] 2025-06-26 12:04:18.566 Aws::Config::AWSConfigFileProfileConfigLoader [140477567127616] Initializing config loader against fileName /home/<USER>/.aws/credentials and using profilePrefix = 0
[INFO] 2025-06-26 12:04:18.566 ProfileConfigFileAWSCredentialsProvider [140477567127616] Setting provider to read credentials from /home/<USER>/.aws/credentials for credentials file and /home/<USER>/.aws/config for the config file , for use with profile default
[INFO] 2025-06-26 12:04:18.566 ProcessCredentialsProvider [140477567127616] Setting process credentials provider to read config from default
[WARN] 2025-06-26 12:04:18.566 STSAssumeRoleWithWebIdentityCredentialsProvider [140477567127616] Token file must be specified to use STS AssumeRole web identity creds provider.
[INFO] 2025-06-26 12:04:18.566 SSOCredentialsProvider [140477567127616] Setting sso credentials provider to read config from default
[INFO] 2025-06-26 12:04:18.566 InstanceProfileCredentialsProvider [140477567127616] Creating Instance with default EC2MetadataClient and refresh rate 300000
[INFO] 2025-06-26 12:04:18.566 DefaultAWSCredentialsProviderChain [140477567127616] Added EC2 metadata service credentials provider to the provider chain.
[INFO] 2025-06-26 12:04:18.566 Aws::Config::AWSProfileConfigLoader [140477567127616] Successfully reloaded configuration.
[INFO] 2025-06-26 12:04:18.566 CurlHandleContainer [140477567127616] Initializing CurlHandleContainer with size 25
[INFO] 2025-06-26 12:04:18.566 CurlHandleContainer [140477164676864] Pool grown by 2
[INFO] 2025-06-26 12:04:18.566 CurlHandleContainer [140477164676864] Connection has been released. Continuing.
[INFO] 2025-06-26 12:04:18.576 CurlHandleContainer [140477156284160] Connection has been released. Continuing.
[INFO] 2025-06-26 12:04:18.586 CurlHandleContainer [140477147891456] Pool grown by 4
[INFO] 2025-06-26 12:04:18.586 CurlHandleContainer [140477147891456] Connection has been released. Continuing.
[INFO] 2025-06-26 12:04:18.597 CurlHandleContainer [140477139498752] Connection has been released. Continuing.
[WARN] 2025-06-26 12:04:19.027 AWSErrorMarshaller [140477147891456] Encountered Unknown AWSError 'InvalidBucketName': The specified bucket is not valid.
[ERROR] 2025-06-26 12:04:19.027 AWSClient [140477147891456] HTTP response code: 400
Resolved remote host IP address: **********
Request ID: DEDZJWYER9G1B7ZK
Exception name: InvalidBucketName
Error message: Unable to parse ExceptionName: InvalidBucketName Message: The specified bucket is not valid.
7 response headers:
connection : close
content-type : application/xml
date : Thu, 26 Jun 2025 12:04:18 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : rE/RKzYr2T8fftP1AWYeRXbmaWlzL6A71wY32YntGUkw5WjThb+MNiOa86o15N6MgPfeqgbyxZBlFk9b6V9pudpvhCvT4KWXA8Uhq1OgVu0=
x-amz-request-id : DEDZJWYER9G1B7ZK
[WARN] 2025-06-26 12:04:19.027 AWSClient [140477147891456] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 12:04:19.028 CurlHandleContainer [140477147891456] Connection has been released. Continuing.
[WARN] 2025-06-26 12:04:19.032 AWSErrorMarshaller [140477139498752] Encountered Unknown AWSError 'InvalidBucketName': The specified bucket is not valid.
[ERROR] 2025-06-26 12:04:19.032 AWSClient [140477139498752] HTTP response code: 400
Resolved remote host IP address: **********
Request ID: DEDTT8K01WD45F08
Exception name: InvalidBucketName
Error message: Unable to parse ExceptionName: InvalidBucketName Message: The specified bucket is not valid.
7 response headers:
connection : close
content-type : application/xml
date : Thu, 26 Jun 2025 12:04:18 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : bLVSnFFg2Hqv20dBPV/ni6kpD2pvAF0h0/eR+PAyu6VUEuXqv8YdwnsN4effVgjwXq3p9jRScNZPHXmTh4XOUKXwyntIvBMGaftlsLWJCYE=
x-amz-request-id : DEDTT8K01WD45F08
[WARN] 2025-06-26 12:04:19.032 AWSClient [140477139498752] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 12:04:19.032 CurlHandleContainer [140477139498752] Connection has been released. Continuing.
[WARN] 2025-06-26 12:04:19.048 AWSErrorMarshaller [140477164676864] Encountered Unknown AWSError 'InvalidBucketName': The specified bucket is not valid.
[ERROR] 2025-06-26 12:04:19.048 AWSClient [140477164676864] HTTP response code: 400
Resolved remote host IP address: *************
Request ID: DEDMS726QR6C1E1Y
Exception name: InvalidBucketName
Error message: Unable to parse ExceptionName: InvalidBucketName Message: The specified bucket is not valid.
7 response headers:
connection : close
content-type : application/xml
date : Thu, 26 Jun 2025 12:04:17 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : d8y74S1T3Ge9dLLiEYJ+UCmSD+uhQl6pEWmJNzGpVOEL9RqwH1rLUGgJULLHHmgOkY4JrK5CuI4=
x-amz-request-id : DEDMS726QR6C1E1Y
[WARN] 2025-06-26 12:04:19.048 AWSClient [140477164676864] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 12:04:19.049 CurlHandleContainer [140477164676864] Connection has been released. Continuing.
[WARN] 2025-06-26 12:04:19.058 AWSErrorMarshaller [140477156284160] Encountered Unknown AWSError 'InvalidBucketName': The specified bucket is not valid.
[ERROR] 2025-06-26 12:04:19.059 AWSClient [140477156284160] HTTP response code: 400
Resolved remote host IP address: **********
Request ID: DEDGMWBT07QR4H2D
Exception name: InvalidBucketName
Error message: Unable to parse ExceptionName: InvalidBucketName Message: The specified bucket is not valid.
7 response headers:
connection : close
content-type : application/xml
date : Thu, 26 Jun 2025 12:04:18 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : v4QsnCPFCmzeh75XuqS/JOX1RH421j009Ex0l/JGUkFt5K7em1weIsbsQarXMt4LHuXxc0yW01hF09ijvZMdXFDBi2Db/ycgmZf22mD8a14=
x-amz-request-id : DEDGMWBT07QR4H2D
[WARN] 2025-06-26 12:04:19.059 AWSClient [140477156284160] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 12:04:19.059 CurlHandleContainer [140477156284160] Connection has been released. Continuing.
[WARN] 2025-06-26 12:04:19.506 AWSErrorMarshaller [140477156284160] Encountered Unknown AWSError 'InvalidBucketName': The specified bucket is not valid.
[ERROR] 2025-06-26 12:04:19.506 AWSClient [140477156284160] HTTP response code: 400
Resolved remote host IP address: **********
Request ID: EKGXZ3RDN1DGS3AQ
Exception name: InvalidBucketName
Error message: Unable to parse ExceptionName: InvalidBucketName Message: The specified bucket is not valid.
7 response headers:
connection : close
content-type : application/xml
date : Thu, 26 Jun 2025 12:04:18 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : E9RYcJPunU0GiuHG700fTZd+1xl5yTEX/M2yFx4904FFVqNXwWPiFsHQKAHRm/KYhC+TxUYaRKT2wg3mSF+foC+vpTg+QvXd6ZCs/RrANfg=
x-amz-request-id : EKGXZ3RDN1DGS3AQ
[WARN] 2025-06-26 12:04:19.506 AWSClient [140477156284160] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 12:04:19.506 CurlHandleContainer [140477156284160] Connection has been released. Continuing.
[WARN] 2025-06-26 12:04:19.510 AWSErrorMarshaller [140477139498752] Encountered Unknown AWSError 'InvalidBucketName': The specified bucket is not valid.
[ERROR] 2025-06-26 12:04:19.510 AWSClient [140477139498752] HTTP response code: 400
Resolved remote host IP address: **********
Request ID: EKGS8MCWVQ7Z85T8
Exception name: InvalidBucketName
Error message: Unable to parse ExceptionName: InvalidBucketName Message: The specified bucket is not valid.
7 response headers:
connection : close
content-type : application/xml
date : Thu, 26 Jun 2025 12:04:18 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : 61vuz8caqMUC1mJ2Kglh8W6StjMMyhiUrANE0VWM5++28Bila8Kht5VDFCu0ifwH32o4kNXcrVrsB+QNNP2UjbdyDfsvJ8S+Nm//fntLbX4=
x-amz-request-id : EKGS8MCWVQ7Z85T8
[WARN] 2025-06-26 12:04:19.510 AWSClient [140477139498752] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 12:04:19.510 CurlHandleContainer [140477139498752] Connection has been released. Continuing.
[WARN] 2025-06-26 12:04:19.529 AWSErrorMarshaller [140477147891456] Encountered Unknown AWSError 'InvalidBucketName': The specified bucket is not valid.
[ERROR] 2025-06-26 12:04:19.529 AWSClient [140477147891456] HTTP response code: 400
Resolved remote host IP address: **********
Request ID: EKGYWMDP1RXSP08J
Exception name: InvalidBucketName
Error message: Unable to parse ExceptionName: InvalidBucketName Message: The specified bucket is not valid.
7 response headers:
connection : close
content-type : application/xml
date : Thu, 26 Jun 2025 12:04:18 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : Fbxw7YyS88kFlIr/uEhzfTia7rSdqpETlLG4MHFjJSexqJrEvl3NE4XqgG5KLTth7mf+PXR4ypOs0h7jvOOcAUp6e2ykgyZ0vr9Mks8lDfI=
x-amz-request-id : EKGYWMDP1RXSP08J
[WARN] 2025-06-26 12:04:19.529 AWSClient [140477147891456] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[WARN] 2025-06-26 12:04:19.531 AWSErrorMarshaller [140477164676864] Encountered Unknown AWSError 'InvalidBucketName': The specified bucket is not valid.
[ERROR] 2025-06-26 12:04:19.531 AWSClient [140477164676864] HTTP response code: 400
Resolved remote host IP address: *************
Request ID: EKGXMFKXTCQ4B7EF
Exception name: InvalidBucketName
Error message: Unable to parse ExceptionName: InvalidBucketName Message: The specified bucket is not valid.
7 response headers:
connection : close
content-type : application/xml
date : Thu, 26 Jun 2025 12:04:19 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : yXIUPjrR2Tn332baHvY1O539Fle4QkmU1CvoSKdj4w9ql+AqcduTSsjAmeGVRBAnW4zwi1OH4jQ=
x-amz-request-id : EKGXMFKXTCQ4B7EF
[WARN] 2025-06-26 12:04:19.531 AWSClient [140477164676864] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[WARN] 2025-06-26 12:04:19.988 AWSErrorMarshaller [140477139498752] Encountered Unknown AWSError 'InvalidBucketName': The specified bucket is not valid.
[ERROR] 2025-06-26 12:04:19.988 AWSClient [140477139498752] HTTP response code: 400
Resolved remote host IP address: **********
Request ID: EKGMEACYGF44VBD3
Exception name: InvalidBucketName
Error message: Unable to parse ExceptionName: InvalidBucketName Message: The specified bucket is not valid.
7 response headers:
connection : close
content-type : application/xml
date : Thu, 26 Jun 2025 12:04:19 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : a6V4p8mlj235OmygsoFMjnT06iPvZbHN++70OUrYFlwQgLWtZdO89NQf6+vV+IdfTeDdh/b518EEeeB+RzxRnMX8c4XrH2+ZqLugN2o8J2k=
x-amz-request-id : EKGMEACYGF44VBD3
[WARN] 2025-06-26 12:04:19.989 AWSClient [140477139498752] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[WARN] 2025-06-26 12:04:20.007 AWSErrorMarshaller [140477156284160] Encountered Unknown AWSError 'InvalidBucketName': The specified bucket is not valid.
[ERROR] 2025-06-26 12:04:20.007 AWSClient [140477156284160] HTTP response code: 400
Resolved remote host IP address: **********
Request ID: EKGSGG2S6ZC44SKQ
Exception name: InvalidBucketName
Error message: Unable to parse ExceptionName: InvalidBucketName Message: The specified bucket is not valid.
7 response headers:
connection : close
content-type : application/xml
date : Thu, 26 Jun 2025 12:04:18 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : hKgAeloRN2xFfHI5xV4h3IfdkJf0jkruP4r8kyhuX9hdWRh47mwwthB6Rmv/7aMI+on4qhoSxujS030q4r/xKYwGB0MY57HBP8k3ptgCbjE=
x-amz-request-id : EKGSGG2S6ZC44SKQ
[WARN] 2025-06-26 12:04:20.007 AWSClient [140477156284160] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 12:04:20.007 CurlHandleContainer [140477567127616] Cleaning up CurlHandleContainer.
[INFO] 2025-06-26 12:04:22.008 Aws::Config::AWSConfigFileProfileConfigLoader [140477567127616] Initializing config loader against fileName /home/<USER>/.aws/credentials and using profilePrefix = 0
[INFO] 2025-06-26 12:04:22.008 ProfileConfigFileAWSCredentialsProvider [140477567127616] Setting provider to read credentials from /home/<USER>/.aws/credentials for credentials file and /home/<USER>/.aws/config for the config file , for use with profile default
[INFO] 2025-06-26 12:04:22.008 ProcessCredentialsProvider [140477567127616] Setting process credentials provider to read config from default
[WARN] 2025-06-26 12:04:22.009 STSAssumeRoleWithWebIdentityCredentialsProvider [140477567127616] Token file must be specified to use STS AssumeRole web identity creds provider.
[INFO] 2025-06-26 12:04:22.009 SSOCredentialsProvider [140477567127616] Setting sso credentials provider to read config from default
[INFO] 2025-06-26 12:04:22.009 InstanceProfileCredentialsProvider [140477567127616] Creating Instance with default EC2MetadataClient and refresh rate 300000
[INFO] 2025-06-26 12:04:22.009 DefaultAWSCredentialsProviderChain [140477567127616] Added EC2 metadata service credentials provider to the provider chain.
[INFO] 2025-06-26 12:04:22.009 Aws::Config::AWSProfileConfigLoader [140477567127616] Successfully reloaded configuration.
[WARN] 2025-06-26 12:04:22.009 ClientConfiguration [140477567127616] Retry Strategy will use the default max attempts.
[INFO] 2025-06-26 12:04:22.009 CurlHandleContainer [140477567127616] Initializing CurlHandleContainer with size 25
[INFO] 2025-06-26 12:04:22.009 AuthCredentialsProvider [140477567127616] (id=0x561a91437f70): TLS context not provided, initializing a new one for credentials provider.
[INFO] 2025-06-26 12:06:56.281 Aws_Init_Cleanup [140233605349440] Initiate AWS SDK for C++ with Version:1.9.234
[INFO] 2025-06-26 12:06:56.281 Aws::Config::AWSConfigFileProfileConfigLoader [140233605349440] Initializing config loader against fileName /home/<USER>/.aws/credentials and using profilePrefix = 0
[INFO] 2025-06-26 12:06:56.281 Aws::Config::AWSConfigFileProfileConfigLoader [140233605349440] Initializing config loader against fileName /home/<USER>/.aws/config and using profilePrefix = 1
[INFO] 2025-06-26 12:06:56.281 Aws::Config::AWSProfileConfigLoader [140233605349440] Successfully reloaded configuration.
[INFO] 2025-06-26 12:06:56.281 Aws::Config::AWSProfileConfigLoader [140233605349440] Successfully reloaded configuration.
[INFO] 2025-06-26 12:06:56.281 event-loop [140233605349440] id=0x55c106ed87b0: Initializing edge-triggered epoll
[INFO] 2025-06-26 12:06:56.281 event-loop [140233605349440] id=0x55c106ed87b0: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 12:06:56.281 event-loop [140233605349440] id=0x55c106ed87b0: Starting event-loop thread.
[INFO] 2025-06-26 12:06:56.281 event-loop [140233605349440] id=0x55c106ef9630: Initializing edge-triggered epoll
[INFO] 2025-06-26 12:06:56.281 event-loop [140233605349440] id=0x55c106ef9630: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 12:06:56.281 event-loop [140233605349440] id=0x55c106ef9630: Starting event-loop thread.
[INFO] 2025-06-26 12:06:56.281 event-loop [140233605349440] id=0x55c106ed2d70: Initializing edge-triggered epoll
[INFO] 2025-06-26 12:06:56.281 event-loop [140233605349440] id=0x55c106ed2d70: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 12:06:56.281 event-loop [140233605349440] id=0x55c106ed2d70: Starting event-loop thread.
[INFO] 2025-06-26 12:06:56.281 event-loop [140233596942080] id=0x55c106ed87b0: main loop started
[INFO] 2025-06-26 12:06:56.281 event-loop [140233596942080] id=0x55c106ed87b0: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 12:06:56.281 event-loop [140233605349440] id=0x55c106ec6340: Initializing edge-triggered epoll
[INFO] 2025-06-26 12:06:56.281 event-loop [140233605349440] id=0x55c106ec6340: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 12:06:56.281 event-loop [140233605349440] id=0x55c106ec6340: Starting event-loop thread.
[INFO] 2025-06-26 12:06:56.281 event-loop [140233588549376] id=0x55c106ef9630: main loop started
[INFO] 2025-06-26 12:06:56.281 event-loop [140233605349440] id=0x55c106ebe050: Initializing edge-triggered epoll
[INFO] 2025-06-26 12:06:56.281 event-loop [140233588549376] id=0x55c106ef9630: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 12:06:56.281 event-loop [140233605349440] id=0x55c106ebe050: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 12:06:56.281 event-loop [140233605349440] id=0x55c106ebe050: Starting event-loop thread.
[INFO] 2025-06-26 12:06:56.281 event-loop [140233605349440] id=0x55c106ead540: Initializing edge-triggered epoll
[INFO] 2025-06-26 12:06:56.281 event-loop [140233580156672] id=0x55c106ed2d70: main loop started
[INFO] 2025-06-26 12:06:56.281 event-loop [140233605349440] id=0x55c106ead540: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 12:06:56.281 event-loop [140233605349440] id=0x55c106ead540: Starting event-loop thread.
[INFO] 2025-06-26 12:06:56.281 event-loop [140233580156672] id=0x55c106ed2d70: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 12:06:56.281 event-loop [140233366566656] id=0x55c106ec6340: main loop started
[INFO] 2025-06-26 12:06:56.281 dns [140233605349440] id=0x55c106f788e0: Initializing default host resolver with 8 max host entries.
[INFO] 2025-06-26 12:06:56.281 event-loop [140233366566656] id=0x55c106ec6340: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 12:06:56.281 channel-bootstrap [140233605349440] id=0x55c106f78980: Initializing client bootstrap with event-loop group 0x55c106f07f80
[INFO] 2025-06-26 12:06:56.281 event-loop [140233358173952] id=0x55c106ebe050: main loop started
[INFO] 2025-06-26 12:06:56.281 event-loop [140233358173952] id=0x55c106ebe050: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 12:06:56.281 event-loop [140233349781248] id=0x55c106ead540: main loop started
[INFO] 2025-06-26 12:06:56.281 event-loop [140233349781248] id=0x55c106ead540: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 12:06:56.304 CurlHttpClient [140233605349440] Initializing Curl library with version: 7.86.0, ssl version: OpenSSL/3.1.2
[INFO] 2025-06-26 12:06:56.304 EC2MetadataClient [140233605349440] Using IMDS endpoint: http://***************
[WARN] 2025-06-26 12:06:56.305 ClientConfiguration [140233605349440] Retry Strategy will use the default max attempts.
[INFO] 2025-06-26 12:06:56.305 EC2MetadataClient [140233605349440] Creating AWSHttpResourceClient with max connections 2 and scheme http
[INFO] 2025-06-26 12:06:56.305 CurlHandleContainer [140233605349440] Initializing CurlHandleContainer with size 2
[WARN] 2025-06-26 12:06:56.305 ClientConfiguration [140233605349440] Retry Strategy will use the default max attempts.
[INFO] 2025-06-26 12:06:56.305 Aws::Config::AWSConfigFileProfileConfigLoader [140233605349440] Initializing config loader against fileName /home/<USER>/.aws/credentials and using profilePrefix = 0
[INFO] 2025-06-26 12:06:56.305 ProfileConfigFileAWSCredentialsProvider [140233605349440] Setting provider to read credentials from /home/<USER>/.aws/credentials for credentials file and /home/<USER>/.aws/config for the config file , for use with profile default
[INFO] 2025-06-26 12:06:56.305 ProcessCredentialsProvider [140233605349440] Setting process credentials provider to read config from default
[WARN] 2025-06-26 12:06:56.305 STSAssumeRoleWithWebIdentityCredentialsProvider [140233605349440] Token file must be specified to use STS AssumeRole web identity creds provider.
[INFO] 2025-06-26 12:06:56.305 SSOCredentialsProvider [140233605349440] Setting sso credentials provider to read config from default
[INFO] 2025-06-26 12:06:56.305 InstanceProfileCredentialsProvider [140233605349440] Creating Instance with default EC2MetadataClient and refresh rate 300000
[INFO] 2025-06-26 12:06:56.305 DefaultAWSCredentialsProviderChain [140233605349440] Added EC2 metadata service credentials provider to the provider chain.
[INFO] 2025-06-26 12:06:56.305 Aws::Config::AWSProfileConfigLoader [140233605349440] Successfully reloaded configuration.
[INFO] 2025-06-26 12:06:56.305 CurlHandleContainer [140233605349440] Initializing CurlHandleContainer with size 25
[INFO] 2025-06-26 12:06:56.305 CurlHandleContainer [140233341388544] Pool grown by 2
[INFO] 2025-06-26 12:06:56.305 CurlHandleContainer [140233341388544] Connection has been released. Continuing.
[INFO] 2025-06-26 12:06:56.315 CurlHandleContainer [140233324603136] Connection has been released. Continuing.
[INFO] 2025-06-26 12:06:56.325 CurlHandleContainer [140233332995840] Pool grown by 4
[INFO] 2025-06-26 12:06:56.325 CurlHandleContainer [140233332995840] Connection has been released. Continuing.
[INFO] 2025-06-26 12:06:56.335 CurlHandleContainer [140233316210432] Connection has been released. Continuing.
[WARN] 2025-06-26 12:06:56.765 AWSErrorMarshaller [140233324603136] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:06:56.765 AWSClient [140233324603136] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: EMHS5YDWTF2JYKJA
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:06:55 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : 7Q/Qd9fUaVDzw09xpp2m+VPpwCl4frKGLBuhpcU1g1RdYUczjJnUnkBcV10ClsrwvJTdNkrq5pI=
x-amz-request-id : EMHS5YDWTF2JYKJA
[WARN] 2025-06-26 12:06:56.765 AWSClient [140233324603136] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 12:06:56.766 CurlHandleContainer [140233324603136] Connection has been released. Continuing.
[WARN] 2025-06-26 12:06:56.769 AWSErrorMarshaller [140233341388544] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:06:56.769 AWSClient [140233341388544] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: EMHMGHY7EWG4DPWK
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:06:56 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : 1lobe966DIKXJRDW9431jbAjHuq4GAEmX0vqaC7a8wMhwI6Yis7qb+xyaYgg0j2SVroX8j0bo2M=
x-amz-request-id : EMHMGHY7EWG4DPWK
[WARN] 2025-06-26 12:06:56.769 AWSClient [140233341388544] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 12:06:56.770 CurlHandleContainer [140233341388544] Connection has been released. Continuing.
[WARN] 2025-06-26 12:06:56.783 AWSErrorMarshaller [140233332995840] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:06:56.783 AWSClient [140233332995840] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: EMHZ6HHZFGVDC81Z
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:06:56 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : NsKQklfqhXEQoNkU+q2Z+Lexds2vpGuMgIy82Ag+FJ9ijmAt6P0j85NEyXRCQtBMLOagF55tj2w=
x-amz-request-id : EMHZ6HHZFGVDC81Z
[WARN] 2025-06-26 12:06:56.783 AWSClient [140233332995840] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 12:06:56.784 CurlHandleContainer [140233332995840] Connection has been released. Continuing.
[WARN] 2025-06-26 12:06:56.834 AWSErrorMarshaller [140233316210432] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:06:56.834 AWSClient [140233316210432] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: EMHT8M9ARKPYZF9Y
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:06:56 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : 4G0WXy+9Z/RcI0HEnDApcWcgYvuYslttTRySxL2r8gWp5h4YXXkYymLOPHSvddgO4iOYdGcu08k=
x-amz-request-id : EMHT8M9ARKPYZF9Y
[WARN] 2025-06-26 12:06:56.834 AWSClient [140233316210432] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 12:06:56.834 CurlHandleContainer [140233316210432] Connection has been released. Continuing.
[WARN] 2025-06-26 12:06:56.911 AWSErrorMarshaller [140233324603136] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:06:56.911 AWSClient [140233324603136] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: EMHM6Z3BNMTXPDQN
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:06:55 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : NtwRcUpfcDjUbSkDDsIWnHYPTYQkM8tEeG0Gg9yMEEyjAXnJMsWcnEsC3DESHZq87pQl6PN0Oec=
x-amz-request-id : EMHM6Z3BNMTXPDQN
[WARN] 2025-06-26 12:06:56.911 AWSClient [140233324603136] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 12:06:56.911 CurlHandleContainer [140233324603136] Connection has been released. Continuing.
[WARN] 2025-06-26 12:06:56.920 AWSErrorMarshaller [140233341388544] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:06:56.920 AWSClient [140233341388544] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: EMHN634610D537JC
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:06:56 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : 16viJCcdKdby9LX/NKXSk1CwttEoPrqM7OSQJZlv9JG93v/6UZnEaVrKIkJGTdKyfWcYXj/wQAU=
x-amz-request-id : EMHN634610D537JC
[WARN] 2025-06-26 12:06:56.920 AWSClient [140233341388544] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 12:06:56.921 CurlHandleContainer [140233341388544] Connection has been released. Continuing.
[WARN] 2025-06-26 12:06:56.999 AWSErrorMarshaller [140233316210432] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:06:56.999 AWSClient [140233316210432] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: EMHNHAHE4TFM5E16
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:06:56 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : +DbPweKkcbEp9GvYsrN92JCO8B6WMXINbe9A+m3/eW6pAriJ8XbrleiVV4RS4eg0f+/XaGyPLTM=
x-amz-request-id : EMHNHAHE4TFM5E16
[WARN] 2025-06-26 12:06:56.999 AWSClient [140233316210432] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[WARN] 2025-06-26 12:06:57.059 AWSErrorMarshaller [140233324603136] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:06:57.059 AWSClient [140233324603136] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: EMHRTJB2Y3TSD3AD
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:06:56 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : V2V+QIKqQu4gAyaT8AMRZ0IK/0xGS3vNCTM/iGorfvJr6wG7wM6/CGfqtW9CzUVLpdUHUZhjcaA=
x-amz-request-id : EMHRTJB2Y3TSD3AD
[WARN] 2025-06-26 12:06:57.059 AWSClient [140233324603136] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[WARN] 2025-06-26 12:06:57.068 AWSErrorMarshaller [140233341388544] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:06:57.068 AWSClient [140233341388544] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: EMHJB8246VQPT7Q6
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:06:56 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : 3wNq7PHtB2rAEEl8TxyUd1khewlUFMp8aO8f2EqhirxoiUzy5BRuqc107JEcxKxmVYe2rqxIofI=
x-amz-request-id : EMHJB8246VQPT7Q6
[WARN] 2025-06-26 12:06:57.068 AWSClient [140233341388544] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[WARN] 2025-06-26 12:06:57.270 AWSErrorMarshaller [140233332995840] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:06:57.270 AWSClient [140233332995840] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: EMHQKZCJ1JW2ZF1S
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:06:56 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : 36otnzg65JUDtx5vRjg8VvZL35qIugshxYEStOfrMGT9fsO19O/tgpluLDJXF//AuVNCEp7zKuo=
x-amz-request-id : EMHQKZCJ1JW2ZF1S
[WARN] 2025-06-26 12:06:57.270 AWSClient [140233332995840] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 12:06:57.271 CurlHandleContainer [140233605349440] Cleaning up CurlHandleContainer.
[INFO] 2025-06-26 12:06:59.273 Aws::Config::AWSConfigFileProfileConfigLoader [140233605349440] Initializing config loader against fileName /home/<USER>/.aws/credentials and using profilePrefix = 0
[INFO] 2025-06-26 12:06:59.273 ProfileConfigFileAWSCredentialsProvider [140233605349440] Setting provider to read credentials from /home/<USER>/.aws/credentials for credentials file and /home/<USER>/.aws/config for the config file , for use with profile default
[INFO] 2025-06-26 12:06:59.273 ProcessCredentialsProvider [140233605349440] Setting process credentials provider to read config from default
[WARN] 2025-06-26 12:06:59.273 STSAssumeRoleWithWebIdentityCredentialsProvider [140233605349440] Token file must be specified to use STS AssumeRole web identity creds provider.
[INFO] 2025-06-26 12:06:59.273 SSOCredentialsProvider [140233605349440] Setting sso credentials provider to read config from default
[INFO] 2025-06-26 12:06:59.273 InstanceProfileCredentialsProvider [140233605349440] Creating Instance with default EC2MetadataClient and refresh rate 300000
[INFO] 2025-06-26 12:06:59.273 DefaultAWSCredentialsProviderChain [140233605349440] Added EC2 metadata service credentials provider to the provider chain.
[INFO] 2025-06-26 12:06:59.273 Aws::Config::AWSProfileConfigLoader [140233605349440] Successfully reloaded configuration.
[WARN] 2025-06-26 12:06:59.274 ClientConfiguration [140233605349440] Retry Strategy will use the default max attempts.
[INFO] 2025-06-26 12:06:59.274 CurlHandleContainer [140233605349440] Initializing CurlHandleContainer with size 25
[INFO] 2025-06-26 12:06:59.274 AuthCredentialsProvider [140233605349440] (id=0x55c107127f70): TLS context not provided, initializing a new one for credentials provider.
[INFO] 2025-06-26 12:10:51.445 Aws_Init_Cleanup [139824950353984] Initiate AWS SDK for C++ with Version:1.9.234
[INFO] 2025-06-26 12:10:51.445 Aws::Config::AWSConfigFileProfileConfigLoader [139824950353984] Initializing config loader against fileName /home/<USER>/.aws/credentials and using profilePrefix = 0
[INFO] 2025-06-26 12:10:51.445 Aws::Config::AWSConfigFileProfileConfigLoader [139824950353984] Initializing config loader against fileName /home/<USER>/.aws/config and using profilePrefix = 1
[INFO] 2025-06-26 12:10:51.445 Aws::Config::AWSProfileConfigLoader [139824950353984] Successfully reloaded configuration.
[INFO] 2025-06-26 12:10:51.445 Aws::Config::AWSProfileConfigLoader [139824950353984] Successfully reloaded configuration.
[INFO] 2025-06-26 12:10:51.445 event-loop [139824950353984] id=0x561526e567b0: Initializing edge-triggered epoll
[INFO] 2025-06-26 12:10:51.445 event-loop [139824950353984] id=0x561526e567b0: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 12:10:51.445 event-loop [139824950353984] id=0x561526e567b0: Starting event-loop thread.
[INFO] 2025-06-26 12:10:51.446 event-loop [139824950353984] id=0x561526e77630: Initializing edge-triggered epoll
[INFO] 2025-06-26 12:10:51.446 event-loop [139824950353984] id=0x561526e77630: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 12:10:51.446 event-loop [139824950353984] id=0x561526e77630: Starting event-loop thread.
[INFO] 2025-06-26 12:10:51.446 event-loop [139824950353984] id=0x561526e50d70: Initializing edge-triggered epoll
[INFO] 2025-06-26 12:10:51.446 event-loop [139824950353984] id=0x561526e50d70: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 12:10:51.446 event-loop [139824950353984] id=0x561526e50d70: Starting event-loop thread.
[INFO] 2025-06-26 12:10:51.446 event-loop [139824941946624] id=0x561526e567b0: main loop started
[INFO] 2025-06-26 12:10:51.446 event-loop [139824941946624] id=0x561526e567b0: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 12:10:51.446 event-loop [139824950353984] id=0x561526e44340: Initializing edge-triggered epoll
[INFO] 2025-06-26 12:10:51.446 event-loop [139824950353984] id=0x561526e44340: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 12:10:51.446 event-loop [139824950353984] id=0x561526e44340: Starting event-loop thread.
[INFO] 2025-06-26 12:10:51.446 event-loop [139824933553920] id=0x561526e77630: main loop started
[INFO] 2025-06-26 12:10:51.446 event-loop [139824925161216] id=0x561526e50d70: main loop started
[INFO] 2025-06-26 12:10:51.446 event-loop [139824933553920] id=0x561526e77630: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 12:10:51.446 event-loop [139824925161216] id=0x561526e50d70: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 12:10:51.446 event-loop [139824950353984] id=0x561526e3c050: Initializing edge-triggered epoll
[INFO] 2025-06-26 12:10:51.446 event-loop [139824950353984] id=0x561526e3c050: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 12:10:51.446 event-loop [139824950353984] id=0x561526e3c050: Starting event-loop thread.
[INFO] 2025-06-26 12:10:51.446 event-loop [139824950353984] id=0x561526e2b540: Initializing edge-triggered epoll
[INFO] 2025-06-26 12:10:51.446 event-loop [139824950353984] id=0x561526e2b540: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 12:10:51.446 event-loop [139824950353984] id=0x561526e2b540: Starting event-loop thread.
[INFO] 2025-06-26 12:10:51.446 event-loop [139824916768512] id=0x561526e44340: main loop started
[INFO] 2025-06-26 12:10:51.446 dns [139824950353984] id=0x561526ef68e0: Initializing default host resolver with 8 max host entries.
[INFO] 2025-06-26 12:10:51.446 channel-bootstrap [139824950353984] id=0x561526ef6980: Initializing client bootstrap with event-loop group 0x561526e85f80
[INFO] 2025-06-26 12:10:51.446 event-loop [139824916768512] id=0x561526e44340: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 12:10:51.446 event-loop [139824908375808] id=0x561526e3c050: main loop started
[INFO] 2025-06-26 12:10:51.446 event-loop [139824908375808] id=0x561526e3c050: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 12:10:51.446 event-loop [139824899983104] id=0x561526e2b540: main loop started
[INFO] 2025-06-26 12:10:51.446 event-loop [139824899983104] id=0x561526e2b540: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 12:10:51.470 CurlHttpClient [139824950353984] Initializing Curl library with version: 7.86.0, ssl version: OpenSSL/3.1.2
[INFO] 2025-06-26 12:10:51.470 EC2MetadataClient [139824950353984] Using IMDS endpoint: http://***************
[WARN] 2025-06-26 12:10:51.470 ClientConfiguration [139824950353984] Retry Strategy will use the default max attempts.
[INFO] 2025-06-26 12:10:51.470 EC2MetadataClient [139824950353984] Creating AWSHttpResourceClient with max connections 2 and scheme http
[INFO] 2025-06-26 12:10:51.470 CurlHandleContainer [139824950353984] Initializing CurlHandleContainer with size 2
[WARN] 2025-06-26 12:10:51.470 ClientConfiguration [139824950353984] Retry Strategy will use the default max attempts.
[INFO] 2025-06-26 12:10:51.470 Aws::Config::AWSConfigFileProfileConfigLoader [139824950353984] Initializing config loader against fileName /home/<USER>/.aws/credentials and using profilePrefix = 0
[INFO] 2025-06-26 12:10:51.470 ProfileConfigFileAWSCredentialsProvider [139824950353984] Setting provider to read credentials from /home/<USER>/.aws/credentials for credentials file and /home/<USER>/.aws/config for the config file , for use with profile default
[INFO] 2025-06-26 12:10:51.470 ProcessCredentialsProvider [139824950353984] Setting process credentials provider to read config from default
[WARN] 2025-06-26 12:10:51.470 STSAssumeRoleWithWebIdentityCredentialsProvider [139824950353984] Token file must be specified to use STS AssumeRole web identity creds provider.
[INFO] 2025-06-26 12:10:51.470 SSOCredentialsProvider [139824950353984] Setting sso credentials provider to read config from default
[INFO] 2025-06-26 12:10:51.470 InstanceProfileCredentialsProvider [139824950353984] Creating Instance with default EC2MetadataClient and refresh rate 300000
[INFO] 2025-06-26 12:10:51.470 DefaultAWSCredentialsProviderChain [139824950353984] Added EC2 metadata service credentials provider to the provider chain.
[INFO] 2025-06-26 12:10:51.470 Aws::Config::AWSProfileConfigLoader [139824950353984] Successfully reloaded configuration.
[INFO] 2025-06-26 12:10:51.470 CurlHandleContainer [139824950353984] Initializing CurlHandleContainer with size 25
[INFO] 2025-06-26 12:10:51.471 CurlHandleContainer [139824891590400] Pool grown by 2
[INFO] 2025-06-26 12:10:51.471 CurlHandleContainer [139824891590400] Connection has been released. Continuing.
[INFO] 2025-06-26 12:10:51.481 CurlHandleContainer [139824472258304] Connection has been released. Continuing.
[INFO] 2025-06-26 12:10:51.491 CurlHandleContainer [139824463865600] Pool grown by 4
[INFO] 2025-06-26 12:10:51.491 CurlHandleContainer [139824463865600] Connection has been released. Continuing.
[INFO] 2025-06-26 12:10:51.501 CurlHandleContainer [139824455472896] Connection has been released. Continuing.
[WARN] 2025-06-26 12:10:51.962 AWSErrorMarshaller [139824891590400] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:10:51.962 AWSClient [139824891590400] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: CXYN5Z8PZQ38DF70
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:10:51 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : FEYKPO9u60oC2YAOxgLVfFeVN7GDpPuE6RP4+BIEKihayjaesQc96V1J77RtiwQgSZKPIn2d9R0=
x-amz-request-id : CXYN5Z8PZQ38DF70
[WARN] 2025-06-26 12:10:51.962 AWSClient [139824891590400] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 12:10:51.963 CurlHandleContainer [139824891590400] Connection has been released. Continuing.
[WARN] 2025-06-26 12:10:51.994 AWSErrorMarshaller [139824463865600] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:10:51.994 AWSClient [139824463865600] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: CXYJ69V8F5Z3F1E5
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:10:51 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : sjzGJnBdC1e/Gdv0xFuquvAh5+8cki5HRIZCUxQ5R6plVK0o9SK7lZMZNn2fMi48N/LUWmTdPMo=
x-amz-request-id : CXYJ69V8F5Z3F1E5
[WARN] 2025-06-26 12:10:51.994 AWSClient [139824463865600] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 12:10:51.994 CurlHandleContainer [139824463865600] Connection has been released. Continuing.
[WARN] 2025-06-26 12:10:51.995 AWSErrorMarshaller [139824472258304] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:10:51.995 AWSClient [139824472258304] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: CXYQQC0KQQT0H4Y1
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:10:51 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : HpLeSEv5Rs2U/KG0BqYi6zCOlmxDQ0rtBGKHI5FqnlrkUhrE8TzJU6zVZ/cIWVDTpXzemdaozXI=
x-amz-request-id : CXYQQC0KQQT0H4Y1
[WARN] 2025-06-26 12:10:51.995 AWSClient [139824472258304] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 12:10:51.996 CurlHandleContainer [139824472258304] Connection has been released. Continuing.
[WARN] 2025-06-26 12:10:52.030 AWSErrorMarshaller [139824455472896] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:10:52.030 AWSClient [139824455472896] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: CXYJB810TASN9FFJ
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:10:51 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : nIPfnQxxgR8GvL/c3otnU+hdzRSA7zEf8jsQ2GMQdGyV89JDXSjvfRj47XJ7qbGSzCzB7h6tNiY=
x-amz-request-id : CXYJB810TASN9FFJ
[WARN] 2025-06-26 12:10:52.030 AWSClient [139824455472896] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 12:10:52.031 CurlHandleContainer [139824455472896] Connection has been released. Continuing.
[WARN] 2025-06-26 12:10:52.124 AWSErrorMarshaller [139824891590400] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:10:52.124 AWSClient [139824891590400] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: Y6DVDAK1NT3MAHKH
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:10:51 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : rrmEJU6q6DxtpRKTn66JMuh1+bSKU88aCPlT8ydEPh3lgEoVnttnfdGyohq4FvNmtB5m3PJRPIY=
x-amz-request-id : Y6DVDAK1NT3MAHKH
[WARN] 2025-06-26 12:10:52.124 AWSClient [139824891590400] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 12:10:52.125 CurlHandleContainer [139824891590400] Connection has been released. Continuing.
[WARN] 2025-06-26 12:10:52.159 AWSErrorMarshaller [139824463865600] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:10:52.159 AWSClient [139824463865600] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: Y6DGXMJRAQ65SSZW
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:10:51 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : 8PKgyWGahFFUr7lyrZ5XpFDm9KWMq47Fi7Hl8Btirj3PmEq5k4Lcxi9UeT7OQz/MAB2+7zUXHU8=
x-amz-request-id : Y6DGXMJRAQ65SSZW
[WARN] 2025-06-26 12:10:52.159 AWSClient [139824463865600] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 12:10:52.160 CurlHandleContainer [139824463865600] Connection has been released. Continuing.
[WARN] 2025-06-26 12:10:52.163 AWSErrorMarshaller [139824472258304] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:10:52.163 AWSClient [139824472258304] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: Y6DHDACRXGHBE070
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:10:51 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : qWbW1h0YovtsLKHZwN+dy3TAs3u1jbNRPbqXr8O7Fil4K7kSDYuh/nYWNM3QcdG+sTg8qQwrm80=
x-amz-request-id : Y6DHDACRXGHBE070
[WARN] 2025-06-26 12:10:52.163 AWSClient [139824472258304] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[WARN] 2025-06-26 12:10:52.196 AWSErrorMarshaller [139824455472896] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:10:52.196 AWSClient [139824455472896] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: Y6DZD774KP9SBXQ2
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:10:51 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : Wpg6iCumtTB3T/4JMTTPYz5J+v7EMmWPi5kysWGPfo8VaC2Lz85BADFbCWL7xP1HQEdEcqhTG7w=
x-amz-request-id : Y6DZD774KP9SBXQ2
[WARN] 2025-06-26 12:10:52.196 AWSClient [139824455472896] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[WARN] 2025-06-26 12:10:52.285 AWSErrorMarshaller [139824891590400] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:10:52.285 AWSClient [139824891590400] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: Y6DVY3V9RZPQ8CME
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:10:51 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : 1/MKxpGKEuZKnrD9zNzf59G/7uu20iG6FY+0I1LfBocOeKymY4Cmkddo8PDon9WtnTDxft+inuA=
x-amz-request-id : Y6DVY3V9RZPQ8CME
[WARN] 2025-06-26 12:10:52.285 AWSClient [139824891590400] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[WARN] 2025-06-26 12:10:52.327 AWSErrorMarshaller [139824463865600] Encountered AWSError 'NoSuchKey': The specified key does not exist.
[ERROR] 2025-06-26 12:10:52.327 AWSClient [139824463865600] HTTP response code: 404
Resolved remote host IP address: *************
Request ID: Y6DN5PYD2G3NZEK2
Exception name: NoSuchKey
Error message: The specified key does not exist.
6 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 12:10:51 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-id-2 : B1gGtrL4LaW9sfqOhfZjVjoZ96GI1yHXxtBsaeGs7sHqT+0dWWGUQlKoPBtv4WNJJUB0hjQ9i4g=
x-amz-request-id : Y6DN5PYD2G3NZEK2
[WARN] 2025-06-26 12:10:52.327 AWSClient [139824463865600] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 12:10:52.327 CurlHandleContainer [139824950353984] Cleaning up CurlHandleContainer.
[INFO] 2025-06-26 12:10:54.337 Aws::Config::AWSConfigFileProfileConfigLoader [139824950353984] Initializing config loader against fileName /home/<USER>/.aws/credentials and using profilePrefix = 0
[INFO] 2025-06-26 12:10:54.338 ProfileConfigFileAWSCredentialsProvider [139824950353984] Setting provider to read credentials from /home/<USER>/.aws/credentials for credentials file and /home/<USER>/.aws/config for the config file , for use with profile default
[INFO] 2025-06-26 12:10:54.338 ProcessCredentialsProvider [139824950353984] Setting process credentials provider to read config from default
[WARN] 2025-06-26 12:10:54.338 STSAssumeRoleWithWebIdentityCredentialsProvider [139824950353984] Token file must be specified to use STS AssumeRole web identity creds provider.
[INFO] 2025-06-26 12:10:54.338 SSOCredentialsProvider [139824950353984] Setting sso credentials provider to read config from default
[INFO] 2025-06-26 12:10:54.338 InstanceProfileCredentialsProvider [139824950353984] Creating Instance with default EC2MetadataClient and refresh rate 300000
[INFO] 2025-06-26 12:10:54.338 DefaultAWSCredentialsProviderChain [139824950353984] Added EC2 metadata service credentials provider to the provider chain.
[INFO] 2025-06-26 12:10:54.338 Aws::Config::AWSProfileConfigLoader [139824950353984] Successfully reloaded configuration.
[WARN] 2025-06-26 12:10:54.338 ClientConfiguration [139824950353984] Retry Strategy will use the default max attempts.
[INFO] 2025-06-26 12:10:54.338 CurlHandleContainer [139824950353984] Initializing CurlHandleContainer with size 25
[INFO] 2025-06-26 12:10:54.338 AuthCredentialsProvider [139824950353984] (id=0x5615270a5f70): TLS context not provided, initializing a new one for credentials provider.
[INFO] 2025-06-26 12:12:36.833 Aws_Init_Cleanup [139923283484736] Initiate AWS SDK for C++ with Version:1.9.234
[INFO] 2025-06-26 12:12:36.833 Aws::Config::AWSConfigFileProfileConfigLoader [139923283484736] Initializing config loader against fileName /home/<USER>/.aws/credentials and using profilePrefix = 0
[INFO] 2025-06-26 12:12:36.833 Aws::Config::AWSConfigFileProfileConfigLoader [139923283484736] Initializing config loader against fileName /home/<USER>/.aws/config and using profilePrefix = 1
[INFO] 2025-06-26 12:12:36.833 Aws::Config::AWSProfileConfigLoader [139923283484736] Successfully reloaded configuration.
[INFO] 2025-06-26 12:18:05.980 Aws_Init_Cleanup [140666022236224] Initiate AWS SDK for C++ with Version:1.9.234
[INFO] 2025-06-26 12:18:05.980 Aws::Config::AWSConfigFileProfileConfigLoader [140666022236224] Initializing config loader against fileName /home/<USER>/.aws/credentials and using profilePrefix = 0
[INFO] 2025-06-26 12:18:05.980 Aws::Config::AWSConfigFileProfileConfigLoader [140666022236224] Initializing config loader against fileName /home/<USER>/.aws/config and using profilePrefix = 1
[INFO] 2025-06-26 12:18:05.980 Aws::Config::AWSProfileConfigLoader [140666022236224] Successfully reloaded configuration.
[INFO] 2025-06-26 12:23:46.131 Aws_Init_Cleanup [140516255420480] Initiate AWS SDK for C++ with Version:1.9.234
[INFO] 2025-06-26 12:23:46.131 Aws::Config::AWSConfigFileProfileConfigLoader [140516255420480] Initializing config loader against fileName /home/<USER>/.aws/credentials and using profilePrefix = 0
[INFO] 2025-06-26 12:23:46.131 Aws::Config::AWSConfigFileProfileConfigLoader [140516255420480] Initializing config loader against fileName /home/<USER>/.aws/config and using profilePrefix = 1
[INFO] 2025-06-26 12:23:46.131 Aws::Config::AWSProfileConfigLoader [140516255420480] Successfully reloaded configuration.
[INFO] 2025-06-26 12:23:46.131 Aws::Config::AWSProfileConfigLoader [140516255420480] Successfully reloaded configuration.
[INFO] 2025-06-26 12:33:07.948 Aws_Init_Cleanup [140107352047680] Initiate AWS SDK for C++ with Version:1.9.234
[INFO] 2025-06-26 12:33:07.948 Aws::Config::AWSConfigFileProfileConfigLoader [140107352047680] Initializing config loader against fileName /home/<USER>/.aws/credentials and using profilePrefix = 0
[INFO] 2025-06-26 12:33:07.948 Aws::Config::AWSConfigFileProfileConfigLoader [140107352047680] Initializing config loader against fileName /home/<USER>/.aws/config and using profilePrefix = 1
[INFO] 2025-06-26 12:33:07.948 Aws::Config::AWSProfileConfigLoader [140107352047680] Successfully reloaded configuration.
[INFO] 2025-06-26 12:33:07.949 Aws::Config::AWSProfileConfigLoader [140107352047680] Successfully reloaded configuration.
[INFO] 2025-06-26 12:49:12.769 Aws_Init_Cleanup [140077839216704] Initiate AWS SDK for C++ with Version:1.9.234
[INFO] 2025-06-26 12:49:12.769 Aws::Config::AWSConfigFileProfileConfigLoader [140077839216704] Initializing config loader against fileName /home/<USER>/.aws/credentials and using profilePrefix = 0
[INFO] 2025-06-26 12:49:12.769 Aws::Config::AWSConfigFileProfileConfigLoader [140077839216704] Initializing config loader against fileName /home/<USER>/.aws/config and using profilePrefix = 1
[INFO] 2025-06-26 12:49:12.769 Aws::Config::AWSProfileConfigLoader [140077839216704] Successfully reloaded configuration.
[INFO] 2025-06-26 12:49:12.769 Aws::Config::AWSProfileConfigLoader [140077839216704] Successfully reloaded configuration.
[INFO] 2025-06-26 12:53:32.214 Aws_Init_Cleanup [140387319668800] Initiate AWS SDK for C++ with Version:1.9.234
[INFO] 2025-06-26 12:53:32.214 Aws::Config::AWSConfigFileProfileConfigLoader [140387319668800] Initializing config loader against fileName /home/<USER>/.aws/credentials and using profilePrefix = 0
[INFO] 2025-06-26 12:53:32.214 Aws::Config::AWSConfigFileProfileConfigLoader [140387319668800] Initializing config loader against fileName /home/<USER>/.aws/config and using profilePrefix = 1
[INFO] 2025-06-26 12:53:32.214 Aws::Config::AWSProfileConfigLoader [140387319668800] Successfully reloaded configuration.
[INFO] 2025-06-26 12:53:32.214 Aws::Config::AWSProfileConfigLoader [140387319668800] Successfully reloaded configuration.
