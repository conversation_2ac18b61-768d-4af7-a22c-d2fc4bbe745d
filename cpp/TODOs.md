Essential works:
* add Metadata interface
* Filter data file id less than delete file id
* Change ctor that throws exceptions to static maker
* Check unhandled return value
* Write unit tests.
* Add comments.
* Add log.
* Reorg the code struct and files.
* Add README
* Write design doc details
* CI/CD
* Solve all todo comments


Some chores / refactor works:
* Clean redundant includes
* Add safety checks
* Add filter creator and checker
* Optimize codes about constant filter
