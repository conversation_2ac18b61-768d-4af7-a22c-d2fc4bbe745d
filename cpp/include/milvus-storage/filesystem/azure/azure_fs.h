// Copyright 2024 Zilliz
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#pragma once
#ifdef MILVUS_AZURE_FS

#include "milvus-storage/filesystem/fs.h"

namespace milvus_storage {

class AzureFileSystemProducer : public FileSystemProducer {
  public:
  AzureFileSystemProducer(const ArrowFileSystemConfig& config) : config_(config) {}

  Result<ArrowFileSystemPtr> Make() override;

  private:
  const ArrowFileSystemConfig config_;
};

}  // namespace milvus_storage
#endif
