[INFO] 2025-06-26 09:58:58.070 Aws_Init_Cleanup [140142877253696] Initiate AWS SDK for C++ with Version:1.9.234
[INFO] 2025-06-26 09:58:58.070 Aws::Config::AWSConfigFileProfileConfigLoader [140142877253696] Initializing config loader against fileName /home/<USER>/.aws/credentials and using profilePrefix = 0
[INFO] 2025-06-26 09:58:58.070 Aws::Config::AWSConfigFileProfileConfigLoader [140142877253696] Initializing config loader against fileName /home/<USER>/.aws/config and using profilePrefix = 1
[INFO] 2025-06-26 09:58:58.070 Aws::Config::AWSProfileConfigLoader [140142877253696] Successfully reloaded configuration.
[INFO] 2025-06-26 09:58:58.070 Aws::Config::AWSProfileConfigLoader [140142877253696] Successfully reloaded configuration.
[INFO] 2025-06-26 09:58:58.070 event-loop [140142877253696] id=0x55c0e5179aa0: Initializing edge-triggered epoll
[INFO] 2025-06-26 09:58:58.070 event-loop [140142877253696] id=0x55c0e5179aa0: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 09:58:58.070 event-loop [140142877253696] id=0x55c0e5179aa0: Starting event-loop thread.
[INFO] 2025-06-26 09:58:58.070 event-loop [140142877253696] id=0x55c0e5194630: Initializing edge-triggered epoll
[INFO] 2025-06-26 09:58:58.070 event-loop [140142877253696] id=0x55c0e5194630: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 09:58:58.070 event-loop [140142877253696] id=0x55c0e5194630: Starting event-loop thread.
[INFO] 2025-06-26 09:58:58.070 event-loop [140142877253696] id=0x55c0e5174930: Initializing edge-triggered epoll
[INFO] 2025-06-26 09:58:58.070 event-loop [140142877253696] id=0x55c0e5174930: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 09:58:58.070 event-loop [140142877253696] id=0x55c0e5174930: Starting event-loop thread.
[INFO] 2025-06-26 09:58:58.070 event-loop [140142868846336] id=0x55c0e5179aa0: main loop started
[INFO] 2025-06-26 09:58:58.070 event-loop [140142868846336] id=0x55c0e5179aa0: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 09:58:58.070 event-loop [140142877253696] id=0x55c0e516a4b0: Initializing edge-triggered epoll
[INFO] 2025-06-26 09:58:58.070 event-loop [140142877253696] id=0x55c0e516a4b0: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 09:58:58.070 event-loop [140142860453632] id=0x55c0e5194630: main loop started
[INFO] 2025-06-26 09:58:58.070 event-loop [140142877253696] id=0x55c0e516a4b0: Starting event-loop thread.
[INFO] 2025-06-26 09:58:58.070 event-loop [140142860453632] id=0x55c0e5194630: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 09:58:58.070 event-loop [140142877253696] id=0x55c0e5151c30: Initializing edge-triggered epoll
[INFO] 2025-06-26 09:58:58.070 event-loop [140142877253696] id=0x55c0e5151c30: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 09:58:58.070 event-loop [140142877253696] id=0x55c0e5151c30: Starting event-loop thread.
[INFO] 2025-06-26 09:58:58.070 event-loop [140142852060928] id=0x55c0e5174930: main loop started
[INFO] 2025-06-26 09:58:58.070 event-loop [140142877253696] id=0x55c0e5157aa0: Initializing edge-triggered epoll
[INFO] 2025-06-26 09:58:58.070 event-loop [140142877253696] id=0x55c0e5157aa0: Using eventfd for cross-thread notifications.
[INFO] 2025-06-26 09:58:58.070 event-loop [140142852060928] id=0x55c0e5174930: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 09:58:58.070 event-loop [140142877253696] id=0x55c0e5157aa0: Starting event-loop thread.
[INFO] 2025-06-26 09:58:58.070 event-loop [140142635382528] id=0x55c0e516a4b0: main loop started
[INFO] 2025-06-26 09:58:58.070 event-loop [140142635382528] id=0x55c0e516a4b0: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 09:58:58.070 dns [140142877253696] id=0x55c0e52138e0: Initializing default host resolver with 8 max host entries.
[INFO] 2025-06-26 09:58:58.070 channel-bootstrap [140142877253696] id=0x55c0e5213980: Initializing client bootstrap with event-loop group 0x55c0e51a2f80
[INFO] 2025-06-26 09:58:58.070 event-loop [140142626989824] id=0x55c0e5151c30: main loop started
[INFO] 2025-06-26 09:58:58.070 event-loop [140142626989824] id=0x55c0e5151c30: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 09:58:58.070 event-loop [140142618597120] id=0x55c0e5157aa0: main loop started
[INFO] 2025-06-26 09:58:58.070 event-loop [140142618597120] id=0x55c0e5157aa0: default timeout 100000, and max events to process per tick 100
[INFO] 2025-06-26 09:58:58.094 CurlHttpClient [140142877253696] Initializing Curl library with version: 7.86.0, ssl version: OpenSSL/3.1.2
[INFO] 2025-06-26 09:58:58.094 EC2MetadataClient [140142877253696] Using IMDS endpoint: http://***************
[WARN] 2025-06-26 09:58:58.094 ClientConfiguration [140142877253696] Retry Strategy will use the default max attempts.
[INFO] 2025-06-26 09:58:58.094 EC2MetadataClient [140142877253696] Creating AWSHttpResourceClient with max connections 2 and scheme http
[INFO] 2025-06-26 09:58:58.094 CurlHandleContainer [140142877253696] Initializing CurlHandleContainer with size 2
[WARN] 2025-06-26 09:58:58.094 ClientConfiguration [140142877253696] Retry Strategy will use the default max attempts.
[INFO] 2025-06-26 09:58:58.094 CurlHandleContainer [140142877253696] Pool grown by 2
[INFO] 2025-06-26 09:58:58.094 CurlHandleContainer [140142877253696] Connection has been released. Continuing.
[ERROR] 2025-06-26 09:58:58.627 CurlHttpClient [140142877253696] Curl returned error code 7 - Couldn't connect to server
[ERROR] 2025-06-26 09:58:58.627 EC2MetadataClient [140142877253696] Http request to retrieve credentials failed
[WARN] 2025-06-26 09:58:58.627 EC2MetadataClient [140142877253696] Request failed, now waiting 0 ms before attempting again.
[INFO] 2025-06-26 09:58:58.627 CurlHandleContainer [140142877253696] Connection has been released. Continuing.
[ERROR] 2025-06-26 09:58:59.629 CurlHttpClient [140142877253696] Curl returned error code 28 - Timeout was reached
[ERROR] 2025-06-26 09:58:59.629 EC2MetadataClient [140142877253696] Http request to retrieve credentials failed
[ERROR] 2025-06-26 09:58:59.629 EC2MetadataClient [140142877253696] Can not retrieve resource from http://***************/latest/meta-data/placement/availability-zone
[INFO] 2025-06-26 09:58:59.629 EC2MetadataClient [140142877253696] Unable to pull region from instance metadata service 
[INFO] 2025-06-26 09:58:59.629 Aws::Config::AWSConfigFileProfileConfigLoader [140142877253696] Initializing config loader against fileName /home/<USER>/.aws/credentials and using profilePrefix = 0
[INFO] 2025-06-26 09:58:59.629 ProfileConfigFileAWSCredentialsProvider [140142877253696] Setting provider to read credentials from /home/<USER>/.aws/credentials for credentials file and /home/<USER>/.aws/config for the config file , for use with profile default
[INFO] 2025-06-26 09:58:59.629 ProcessCredentialsProvider [140142877253696] Setting process credentials provider to read config from default
[WARN] 2025-06-26 09:58:59.629 STSAssumeRoleWithWebIdentityCredentialsProvider [140142877253696] Token file must be specified to use STS AssumeRole web identity creds provider.
[INFO] 2025-06-26 09:58:59.629 SSOCredentialsProvider [140142877253696] Setting sso credentials provider to read config from default
[INFO] 2025-06-26 09:58:59.629 InstanceProfileCredentialsProvider [140142877253696] Creating Instance with default EC2MetadataClient and refresh rate 300000
[INFO] 2025-06-26 09:58:59.629 DefaultAWSCredentialsProviderChain [140142877253696] Added EC2 metadata service credentials provider to the provider chain.
[INFO] 2025-06-26 09:58:59.629 Aws::Config::AWSProfileConfigLoader [140142877253696] Successfully reloaded configuration.
[INFO] 2025-06-26 09:58:59.629 CurlHandleContainer [140142877253696] Initializing CurlHandleContainer with size 25
[INFO] 2025-06-26 09:58:59.630 CurlHandleContainer [140142610204416] Pool grown by 2
[INFO] 2025-06-26 09:58:59.630 CurlHandleContainer [140142610204416] Connection has been released. Continuing.
[INFO] 2025-06-26 09:58:59.640 CurlHandleContainer [140142601811712] Connection has been released. Continuing.
[INFO] 2025-06-26 09:58:59.650 CurlHandleContainer [140142593419008] Pool grown by 4
[INFO] 2025-06-26 09:58:59.650 CurlHandleContainer [140142593419008] Connection has been released. Continuing.
[INFO] 2025-06-26 09:58:59.661 CurlHandleContainer [140142585026304] Connection has been released. Continuing.
[WARN] 2025-06-26 09:59:00.243 AWSErrorMarshaller [140142610204416] Encountered Unknown AWSError 'PermanentRedirect': The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
[ERROR] 2025-06-26 09:59:00.243 AWSClient [140142610204416] HTTP response code: 301
Resolved remote host IP address: **************
Request ID: ZKJHAAPB14BN7AKQ
Exception name: PermanentRedirect
Error message: Unable to parse ExceptionName: PermanentRedirect Message: The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
7 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 09:58:59 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-bucket-region : eu-central-1
x-amz-id-2 : ghrDHI3CgVeNvcq0wafoboZahhEDhbSwmGnUtHsgJ/Gmal2GpwvqL8t3Ps9/YzAIYEx9WuFLzLY=
x-amz-request-id : ZKJHAAPB14BN7AKQ
[WARN] 2025-06-26 09:59:00.243 AWSClient [140142610204416] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 09:59:00.244 CurlHandleContainer [140142610204416] Connection has been released. Continuing.
[WARN] 2025-06-26 09:59:00.245 AWSErrorMarshaller [140142601811712] Encountered Unknown AWSError 'PermanentRedirect': The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
[ERROR] 2025-06-26 09:59:00.245 AWSClient [140142601811712] HTTP response code: 301
Resolved remote host IP address: **************
Request ID: ZKJHZWVV22633Z10
Exception name: PermanentRedirect
Error message: Unable to parse ExceptionName: PermanentRedirect Message: The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
7 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 09:58:59 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-bucket-region : eu-central-1
x-amz-id-2 : OwK+amSAMhiIjuAJX7tDn+zAx1h2a9dl1R0Dq4YxEpt0VbgZ8k/mzHJCLSNnSV+wUA0LnShckho=
x-amz-request-id : ZKJHZWVV22633Z10
[WARN] 2025-06-26 09:59:00.245 AWSClient [140142601811712] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 09:59:00.245 CurlHandleContainer [140142601811712] Connection has been released. Continuing.
[WARN] 2025-06-26 09:59:00.267 AWSErrorMarshaller [140142593419008] Encountered Unknown AWSError 'PermanentRedirect': The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
[ERROR] 2025-06-26 09:59:00.267 AWSClient [140142593419008] HTTP response code: 301
Resolved remote host IP address: **************
Request ID: ZKJQEVAJVVQ10A2A
Exception name: PermanentRedirect
Error message: Unable to parse ExceptionName: PermanentRedirect Message: The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
7 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 09:59:00 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-bucket-region : eu-central-1
x-amz-id-2 : f2jt96PzwXzDzNVijKC3LC/+RHtSyxjC/qdEm1L6anJyPL9W3C5BWwlnht74luzNfvG9J08TAgo=
x-amz-request-id : ZKJQEVAJVVQ10A2A
[WARN] 2025-06-26 09:59:00.267 AWSClient [140142593419008] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 09:59:00.267 CurlHandleContainer [140142593419008] Connection has been released. Continuing.
[WARN] 2025-06-26 09:59:00.275 AWSErrorMarshaller [140142585026304] Encountered Unknown AWSError 'PermanentRedirect': The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
[ERROR] 2025-06-26 09:59:00.275 AWSClient [140142585026304] HTTP response code: 301
Resolved remote host IP address: **************
Request ID: ZKJYPAXDC7K8KZ51
Exception name: PermanentRedirect
Error message: Unable to parse ExceptionName: PermanentRedirect Message: The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
7 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 09:58:59 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-bucket-region : eu-central-1
x-amz-id-2 : C0PHoWf2liMTAVkAJFd75nm684ePMDERdf0gq1IOxHO5gP9x6ze69plEPDo5beZcgHAXVbnAsEE=
x-amz-request-id : ZKJYPAXDC7K8KZ51
[WARN] 2025-06-26 09:59:00.275 AWSClient [140142585026304] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 09:59:00.275 CurlHandleContainer [140142585026304] Connection has been released. Continuing.
[WARN] 2025-06-26 09:59:00.441 AWSErrorMarshaller [140142610204416] Encountered Unknown AWSError 'PermanentRedirect': The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
[ERROR] 2025-06-26 09:59:00.441 AWSClient [140142610204416] HTTP response code: 301
Resolved remote host IP address: **************
Request ID: ZKJK8CPSARQT2RD4
Exception name: PermanentRedirect
Error message: Unable to parse ExceptionName: PermanentRedirect Message: The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
7 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 09:58:59 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-bucket-region : eu-central-1
x-amz-id-2 : +118mWl8tZMOxsGwvFvF5A3kSfX0VIimuuM5gVNk5cgAd8XdBELqJJjEXBwzQulrD002BPVrx3E=
x-amz-request-id : ZKJK8CPSARQT2RD4
[WARN] 2025-06-26 09:59:00.441 AWSClient [140142610204416] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 09:59:00.442 CurlHandleContainer [140142610204416] Connection has been released. Continuing.
[WARN] 2025-06-26 09:59:00.444 AWSErrorMarshaller [140142601811712] Encountered Unknown AWSError 'PermanentRedirect': The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
[ERROR] 2025-06-26 09:59:00.444 AWSClient [140142601811712] HTTP response code: 301
Resolved remote host IP address: **************
Request ID: ZKJX4C3G8BHNJV0Z
Exception name: PermanentRedirect
Error message: Unable to parse ExceptionName: PermanentRedirect Message: The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
7 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 09:59:00 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-bucket-region : eu-central-1
x-amz-id-2 : Pzt5PwTMD0gPAEF0WQbn+dnCdnWKsHrtUBi6KNogK0GecOH0zIJ5Q6GHLaYYHGjitsb3qxX03/E=
x-amz-request-id : ZKJX4C3G8BHNJV0Z
[WARN] 2025-06-26 09:59:00.444 AWSClient [140142601811712] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 09:59:00.445 CurlHandleContainer [140142601811712] Connection has been released. Continuing.
[WARN] 2025-06-26 09:59:00.468 AWSErrorMarshaller [140142593419008] Encountered Unknown AWSError 'PermanentRedirect': The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
[ERROR] 2025-06-26 09:59:00.468 AWSClient [140142593419008] HTTP response code: 301
Resolved remote host IP address: **************
Request ID: ZKJG2QW0H5EPDT2A
Exception name: PermanentRedirect
Error message: Unable to parse ExceptionName: PermanentRedirect Message: The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
7 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 09:59:00 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-bucket-region : eu-central-1
x-amz-id-2 : Bjj/jGNQtGOxTy3H0cObVcjE7YosW9bcMInnTLHHM1FMpsUpkTkah7TWD3Y/9ZL32Sxrrfoxtrs=
x-amz-request-id : ZKJG2QW0H5EPDT2A
[WARN] 2025-06-26 09:59:00.469 AWSClient [140142593419008] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[WARN] 2025-06-26 09:59:00.475 AWSErrorMarshaller [140142585026304] Encountered Unknown AWSError 'PermanentRedirect': The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
[ERROR] 2025-06-26 09:59:00.475 AWSClient [140142585026304] HTTP response code: 301
Resolved remote host IP address: **************
Request ID: ZKJGCZA976G1KC5K
Exception name: PermanentRedirect
Error message: Unable to parse ExceptionName: PermanentRedirect Message: The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
7 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 09:58:59 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-bucket-region : eu-central-1
x-amz-id-2 : GtRVbKuzSSfR+Afb7YOasLFHhsK2mHBJPKn2ZcMn9asFNk9hXTaE9/TA+QGHmi36KkDzjiWKHVc=
x-amz-request-id : ZKJGCZA976G1KC5K
[WARN] 2025-06-26 09:59:00.475 AWSClient [140142585026304] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[WARN] 2025-06-26 09:59:00.639 AWSErrorMarshaller [140142610204416] Encountered Unknown AWSError 'PermanentRedirect': The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
[ERROR] 2025-06-26 09:59:00.639 AWSClient [140142610204416] HTTP response code: 301
Resolved remote host IP address: **************
Request ID: ZKJPKP5DX08EMRKC
Exception name: PermanentRedirect
Error message: Unable to parse ExceptionName: PermanentRedirect Message: The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
7 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 09:59:00 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-bucket-region : eu-central-1
x-amz-id-2 : KbuDTB9mNDEqUmh//9yRRBkq2MxUBHIK2S7rs075ndIysHPt6ohTk1uBzffTKJf8XQXVPoWHY6M=
x-amz-request-id : ZKJPKP5DX08EMRKC
[WARN] 2025-06-26 09:59:00.639 AWSClient [140142610204416] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[WARN] 2025-06-26 09:59:00.643 AWSErrorMarshaller [140142601811712] Encountered Unknown AWSError 'PermanentRedirect': The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
[ERROR] 2025-06-26 09:59:00.643 AWSClient [140142601811712] HTTP response code: 301
Resolved remote host IP address: **************
Request ID: ZKJWBYX4CHSM8769
Exception name: PermanentRedirect
Error message: Unable to parse ExceptionName: PermanentRedirect Message: The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.
7 response headers:
content-type : application/xml
date : Thu, 26 Jun 2025 09:59:00 GMT
server : AmazonS3
transfer-encoding : chunked
x-amz-bucket-region : eu-central-1
x-amz-id-2 : AvHUDUZY10rk4Kij9zcUMunZZAPUuvyMcAnhGBi0brAr9AV1Xc3ClTyJxdjOnWMR6/NWSgAD4R8=
x-amz-request-id : ZKJWBYX4CHSM8769
[WARN] 2025-06-26 09:59:00.643 AWSClient [140142601811712] If the signature check failed. This could be because of a time skew. Attempting to adjust the signer.
[INFO] 2025-06-26 09:59:00.644 CurlHandleContainer [140142877253696] Cleaning up CurlHandleContainer.
