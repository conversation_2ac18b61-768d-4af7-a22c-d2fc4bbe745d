// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: manifest.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_manifest_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_manifest_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021004 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "schema_arrow.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_manifest_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_manifest_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_manifest_2eproto;
namespace manifest_proto {
class Blob;
struct BlobDefaultTypeInternal;
extern BlobDefaultTypeInternal _Blob_default_instance_;
class Fragment;
struct FragmentDefaultTypeInternal;
extern FragmentDefaultTypeInternal _Fragment_default_instance_;
class Manifest;
struct ManifestDefaultTypeInternal;
extern ManifestDefaultTypeInternal _Manifest_default_instance_;
class Options;
struct OptionsDefaultTypeInternal;
extern OptionsDefaultTypeInternal _Options_default_instance_;
}  // namespace manifest_proto
PROTOBUF_NAMESPACE_OPEN
template<> ::manifest_proto::Blob* Arena::CreateMaybeMessage<::manifest_proto::Blob>(Arena*);
template<> ::manifest_proto::Fragment* Arena::CreateMaybeMessage<::manifest_proto::Fragment>(Arena*);
template<> ::manifest_proto::Manifest* Arena::CreateMaybeMessage<::manifest_proto::Manifest>(Arena*);
template<> ::manifest_proto::Options* Arena::CreateMaybeMessage<::manifest_proto::Options>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace manifest_proto {

// ===================================================================

class Options final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:manifest_proto.Options) */ {
 public:
  inline Options() : Options(nullptr) {}
  ~Options() override;
  explicit PROTOBUF_CONSTEXPR Options(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Options(const Options& from);
  Options(Options&& from) noexcept
    : Options() {
    *this = ::std::move(from);
  }

  inline Options& operator=(const Options& from) {
    CopyFrom(from);
    return *this;
  }
  inline Options& operator=(Options&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Options& default_instance() {
    return *internal_default_instance();
  }
  static inline const Options* internal_default_instance() {
    return reinterpret_cast<const Options*>(
               &_Options_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(Options& a, Options& b) {
    a.Swap(&b);
  }
  inline void Swap(Options* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Options* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Options* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Options>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Options& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Options& from) {
    Options::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Options* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "manifest_proto.Options";
  }
  protected:
  explicit Options(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kUriFieldNumber = 1,
  };
  // string uri = 1;
  void clear_uri();
  const std::string& uri() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_uri(ArgT0&& arg0, ArgT... args);
  std::string* mutable_uri();
  PROTOBUF_NODISCARD std::string* release_uri();
  void set_allocated_uri(std::string* uri);
  private:
  const std::string& _internal_uri() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_uri(const std::string& value);
  std::string* _internal_mutable_uri();
  public:

  // @@protoc_insertion_point(class_scope:manifest_proto.Options)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr uri_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_manifest_2eproto;
};
// -------------------------------------------------------------------

class Manifest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:manifest_proto.Manifest) */ {
 public:
  inline Manifest() : Manifest(nullptr) {}
  ~Manifest() override;
  explicit PROTOBUF_CONSTEXPR Manifest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Manifest(const Manifest& from);
  Manifest(Manifest&& from) noexcept
    : Manifest() {
    *this = ::std::move(from);
  }

  inline Manifest& operator=(const Manifest& from) {
    CopyFrom(from);
    return *this;
  }
  inline Manifest& operator=(Manifest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Manifest& default_instance() {
    return *internal_default_instance();
  }
  static inline const Manifest* internal_default_instance() {
    return reinterpret_cast<const Manifest*>(
               &_Manifest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(Manifest& a, Manifest& b) {
    a.Swap(&b);
  }
  inline void Swap(Manifest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Manifest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Manifest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Manifest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Manifest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Manifest& from) {
    Manifest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Manifest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "manifest_proto.Manifest";
  }
  protected:
  explicit Manifest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kScalarFragmentsFieldNumber = 4,
    kVectorFragmentsFieldNumber = 5,
    kDeleteFragmentsFieldNumber = 6,
    kBlobsFieldNumber = 7,
    kOptionsFieldNumber = 2,
    kSchemaFieldNumber = 3,
    kVersionFieldNumber = 1,
  };
  // repeated .manifest_proto.Fragment scalar_fragments = 4;
  int scalar_fragments_size() const;
  private:
  int _internal_scalar_fragments_size() const;
  public:
  void clear_scalar_fragments();
  ::manifest_proto::Fragment* mutable_scalar_fragments(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::manifest_proto::Fragment >*
      mutable_scalar_fragments();
  private:
  const ::manifest_proto::Fragment& _internal_scalar_fragments(int index) const;
  ::manifest_proto::Fragment* _internal_add_scalar_fragments();
  public:
  const ::manifest_proto::Fragment& scalar_fragments(int index) const;
  ::manifest_proto::Fragment* add_scalar_fragments();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::manifest_proto::Fragment >&
      scalar_fragments() const;

  // repeated .manifest_proto.Fragment vector_fragments = 5;
  int vector_fragments_size() const;
  private:
  int _internal_vector_fragments_size() const;
  public:
  void clear_vector_fragments();
  ::manifest_proto::Fragment* mutable_vector_fragments(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::manifest_proto::Fragment >*
      mutable_vector_fragments();
  private:
  const ::manifest_proto::Fragment& _internal_vector_fragments(int index) const;
  ::manifest_proto::Fragment* _internal_add_vector_fragments();
  public:
  const ::manifest_proto::Fragment& vector_fragments(int index) const;
  ::manifest_proto::Fragment* add_vector_fragments();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::manifest_proto::Fragment >&
      vector_fragments() const;

  // repeated .manifest_proto.Fragment delete_fragments = 6;
  int delete_fragments_size() const;
  private:
  int _internal_delete_fragments_size() const;
  public:
  void clear_delete_fragments();
  ::manifest_proto::Fragment* mutable_delete_fragments(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::manifest_proto::Fragment >*
      mutable_delete_fragments();
  private:
  const ::manifest_proto::Fragment& _internal_delete_fragments(int index) const;
  ::manifest_proto::Fragment* _internal_add_delete_fragments();
  public:
  const ::manifest_proto::Fragment& delete_fragments(int index) const;
  ::manifest_proto::Fragment* add_delete_fragments();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::manifest_proto::Fragment >&
      delete_fragments() const;

  // repeated .manifest_proto.Blob blobs = 7;
  int blobs_size() const;
  private:
  int _internal_blobs_size() const;
  public:
  void clear_blobs();
  ::manifest_proto::Blob* mutable_blobs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::manifest_proto::Blob >*
      mutable_blobs();
  private:
  const ::manifest_proto::Blob& _internal_blobs(int index) const;
  ::manifest_proto::Blob* _internal_add_blobs();
  public:
  const ::manifest_proto::Blob& blobs(int index) const;
  ::manifest_proto::Blob* add_blobs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::manifest_proto::Blob >&
      blobs() const;

  // .manifest_proto.Options options = 2;
  bool has_options() const;
  private:
  bool _internal_has_options() const;
  public:
  void clear_options();
  const ::manifest_proto::Options& options() const;
  PROTOBUF_NODISCARD ::manifest_proto::Options* release_options();
  ::manifest_proto::Options* mutable_options();
  void set_allocated_options(::manifest_proto::Options* options);
  private:
  const ::manifest_proto::Options& _internal_options() const;
  ::manifest_proto::Options* _internal_mutable_options();
  public:
  void unsafe_arena_set_allocated_options(
      ::manifest_proto::Options* options);
  ::manifest_proto::Options* unsafe_arena_release_options();

  // .schema_proto.Schema schema = 3;
  bool has_schema() const;
  private:
  bool _internal_has_schema() const;
  public:
  void clear_schema();
  const ::schema_proto::Schema& schema() const;
  PROTOBUF_NODISCARD ::schema_proto::Schema* release_schema();
  ::schema_proto::Schema* mutable_schema();
  void set_allocated_schema(::schema_proto::Schema* schema);
  private:
  const ::schema_proto::Schema& _internal_schema() const;
  ::schema_proto::Schema* _internal_mutable_schema();
  public:
  void unsafe_arena_set_allocated_schema(
      ::schema_proto::Schema* schema);
  ::schema_proto::Schema* unsafe_arena_release_schema();

  // int64 version = 1;
  void clear_version();
  int64_t version() const;
  void set_version(int64_t value);
  private:
  int64_t _internal_version() const;
  void _internal_set_version(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:manifest_proto.Manifest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::manifest_proto::Fragment > scalar_fragments_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::manifest_proto::Fragment > vector_fragments_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::manifest_proto::Fragment > delete_fragments_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::manifest_proto::Blob > blobs_;
    ::manifest_proto::Options* options_;
    ::schema_proto::Schema* schema_;
    int64_t version_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_manifest_2eproto;
};
// -------------------------------------------------------------------

class Fragment final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:manifest_proto.Fragment) */ {
 public:
  inline Fragment() : Fragment(nullptr) {}
  ~Fragment() override;
  explicit PROTOBUF_CONSTEXPR Fragment(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Fragment(const Fragment& from);
  Fragment(Fragment&& from) noexcept
    : Fragment() {
    *this = ::std::move(from);
  }

  inline Fragment& operator=(const Fragment& from) {
    CopyFrom(from);
    return *this;
  }
  inline Fragment& operator=(Fragment&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Fragment& default_instance() {
    return *internal_default_instance();
  }
  static inline const Fragment* internal_default_instance() {
    return reinterpret_cast<const Fragment*>(
               &_Fragment_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(Fragment& a, Fragment& b) {
    a.Swap(&b);
  }
  inline void Swap(Fragment* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Fragment* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Fragment* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Fragment>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Fragment& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Fragment& from) {
    Fragment::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Fragment* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "manifest_proto.Fragment";
  }
  protected:
  explicit Fragment(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFilesFieldNumber = 2,
    kIdFieldNumber = 1,
  };
  // repeated string files = 2;
  int files_size() const;
  private:
  int _internal_files_size() const;
  public:
  void clear_files();
  const std::string& files(int index) const;
  std::string* mutable_files(int index);
  void set_files(int index, const std::string& value);
  void set_files(int index, std::string&& value);
  void set_files(int index, const char* value);
  void set_files(int index, const char* value, size_t size);
  std::string* add_files();
  void add_files(const std::string& value);
  void add_files(std::string&& value);
  void add_files(const char* value);
  void add_files(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& files() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_files();
  private:
  const std::string& _internal_files(int index) const;
  std::string* _internal_add_files();
  public:

  // int64 id = 1;
  void clear_id();
  int64_t id() const;
  void set_id(int64_t value);
  private:
  int64_t _internal_id() const;
  void _internal_set_id(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:manifest_proto.Fragment)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> files_;
    int64_t id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_manifest_2eproto;
};
// -------------------------------------------------------------------

class Blob final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:manifest_proto.Blob) */ {
 public:
  inline Blob() : Blob(nullptr) {}
  ~Blob() override;
  explicit PROTOBUF_CONSTEXPR Blob(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Blob(const Blob& from);
  Blob(Blob&& from) noexcept
    : Blob() {
    *this = ::std::move(from);
  }

  inline Blob& operator=(const Blob& from) {
    CopyFrom(from);
    return *this;
  }
  inline Blob& operator=(Blob&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Blob& default_instance() {
    return *internal_default_instance();
  }
  static inline const Blob* internal_default_instance() {
    return reinterpret_cast<const Blob*>(
               &_Blob_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(Blob& a, Blob& b) {
    a.Swap(&b);
  }
  inline void Swap(Blob* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Blob* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Blob* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Blob>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Blob& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Blob& from) {
    Blob::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Blob* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "manifest_proto.Blob";
  }
  protected:
  explicit Blob(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kFileFieldNumber = 3,
    kSizeFieldNumber = 2,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string file = 3;
  void clear_file();
  const std::string& file() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_file(ArgT0&& arg0, ArgT... args);
  std::string* mutable_file();
  PROTOBUF_NODISCARD std::string* release_file();
  void set_allocated_file(std::string* file);
  private:
  const std::string& _internal_file() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_file(const std::string& value);
  std::string* _internal_mutable_file();
  public:

  // int64 size = 2;
  void clear_size();
  int64_t size() const;
  void set_size(int64_t value);
  private:
  int64_t _internal_size() const;
  void _internal_set_size(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:manifest_proto.Blob)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr file_;
    int64_t size_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_manifest_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Options

// string uri = 1;
inline void Options::clear_uri() {
  _impl_.uri_.ClearToEmpty();
}
inline const std::string& Options::uri() const {
  // @@protoc_insertion_point(field_get:manifest_proto.Options.uri)
  return _internal_uri();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Options::set_uri(ArgT0&& arg0, ArgT... args) {
 
 _impl_.uri_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:manifest_proto.Options.uri)
}
inline std::string* Options::mutable_uri() {
  std::string* _s = _internal_mutable_uri();
  // @@protoc_insertion_point(field_mutable:manifest_proto.Options.uri)
  return _s;
}
inline const std::string& Options::_internal_uri() const {
  return _impl_.uri_.Get();
}
inline void Options::_internal_set_uri(const std::string& value) {
  
  _impl_.uri_.Set(value, GetArenaForAllocation());
}
inline std::string* Options::_internal_mutable_uri() {
  
  return _impl_.uri_.Mutable(GetArenaForAllocation());
}
inline std::string* Options::release_uri() {
  // @@protoc_insertion_point(field_release:manifest_proto.Options.uri)
  return _impl_.uri_.Release();
}
inline void Options::set_allocated_uri(std::string* uri) {
  if (uri != nullptr) {
    
  } else {
    
  }
  _impl_.uri_.SetAllocated(uri, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.uri_.IsDefault()) {
    _impl_.uri_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:manifest_proto.Options.uri)
}

// -------------------------------------------------------------------

// Manifest

// int64 version = 1;
inline void Manifest::clear_version() {
  _impl_.version_ = int64_t{0};
}
inline int64_t Manifest::_internal_version() const {
  return _impl_.version_;
}
inline int64_t Manifest::version() const {
  // @@protoc_insertion_point(field_get:manifest_proto.Manifest.version)
  return _internal_version();
}
inline void Manifest::_internal_set_version(int64_t value) {
  
  _impl_.version_ = value;
}
inline void Manifest::set_version(int64_t value) {
  _internal_set_version(value);
  // @@protoc_insertion_point(field_set:manifest_proto.Manifest.version)
}

// .manifest_proto.Options options = 2;
inline bool Manifest::_internal_has_options() const {
  return this != internal_default_instance() && _impl_.options_ != nullptr;
}
inline bool Manifest::has_options() const {
  return _internal_has_options();
}
inline void Manifest::clear_options() {
  if (GetArenaForAllocation() == nullptr && _impl_.options_ != nullptr) {
    delete _impl_.options_;
  }
  _impl_.options_ = nullptr;
}
inline const ::manifest_proto::Options& Manifest::_internal_options() const {
  const ::manifest_proto::Options* p = _impl_.options_;
  return p != nullptr ? *p : reinterpret_cast<const ::manifest_proto::Options&>(
      ::manifest_proto::_Options_default_instance_);
}
inline const ::manifest_proto::Options& Manifest::options() const {
  // @@protoc_insertion_point(field_get:manifest_proto.Manifest.options)
  return _internal_options();
}
inline void Manifest::unsafe_arena_set_allocated_options(
    ::manifest_proto::Options* options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.options_);
  }
  _impl_.options_ = options;
  if (options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:manifest_proto.Manifest.options)
}
inline ::manifest_proto::Options* Manifest::release_options() {
  
  ::manifest_proto::Options* temp = _impl_.options_;
  _impl_.options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::manifest_proto::Options* Manifest::unsafe_arena_release_options() {
  // @@protoc_insertion_point(field_release:manifest_proto.Manifest.options)
  
  ::manifest_proto::Options* temp = _impl_.options_;
  _impl_.options_ = nullptr;
  return temp;
}
inline ::manifest_proto::Options* Manifest::_internal_mutable_options() {
  
  if (_impl_.options_ == nullptr) {
    auto* p = CreateMaybeMessage<::manifest_proto::Options>(GetArenaForAllocation());
    _impl_.options_ = p;
  }
  return _impl_.options_;
}
inline ::manifest_proto::Options* Manifest::mutable_options() {
  ::manifest_proto::Options* _msg = _internal_mutable_options();
  // @@protoc_insertion_point(field_mutable:manifest_proto.Manifest.options)
  return _msg;
}
inline void Manifest::set_allocated_options(::manifest_proto::Options* options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.options_;
  }
  if (options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(options);
    if (message_arena != submessage_arena) {
      options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.options_ = options;
  // @@protoc_insertion_point(field_set_allocated:manifest_proto.Manifest.options)
}

// .schema_proto.Schema schema = 3;
inline bool Manifest::_internal_has_schema() const {
  return this != internal_default_instance() && _impl_.schema_ != nullptr;
}
inline bool Manifest::has_schema() const {
  return _internal_has_schema();
}
inline const ::schema_proto::Schema& Manifest::_internal_schema() const {
  const ::schema_proto::Schema* p = _impl_.schema_;
  return p != nullptr ? *p : reinterpret_cast<const ::schema_proto::Schema&>(
      ::schema_proto::_Schema_default_instance_);
}
inline const ::schema_proto::Schema& Manifest::schema() const {
  // @@protoc_insertion_point(field_get:manifest_proto.Manifest.schema)
  return _internal_schema();
}
inline void Manifest::unsafe_arena_set_allocated_schema(
    ::schema_proto::Schema* schema) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.schema_);
  }
  _impl_.schema_ = schema;
  if (schema) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:manifest_proto.Manifest.schema)
}
inline ::schema_proto::Schema* Manifest::release_schema() {
  
  ::schema_proto::Schema* temp = _impl_.schema_;
  _impl_.schema_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::schema_proto::Schema* Manifest::unsafe_arena_release_schema() {
  // @@protoc_insertion_point(field_release:manifest_proto.Manifest.schema)
  
  ::schema_proto::Schema* temp = _impl_.schema_;
  _impl_.schema_ = nullptr;
  return temp;
}
inline ::schema_proto::Schema* Manifest::_internal_mutable_schema() {
  
  if (_impl_.schema_ == nullptr) {
    auto* p = CreateMaybeMessage<::schema_proto::Schema>(GetArenaForAllocation());
    _impl_.schema_ = p;
  }
  return _impl_.schema_;
}
inline ::schema_proto::Schema* Manifest::mutable_schema() {
  ::schema_proto::Schema* _msg = _internal_mutable_schema();
  // @@protoc_insertion_point(field_mutable:manifest_proto.Manifest.schema)
  return _msg;
}
inline void Manifest::set_allocated_schema(::schema_proto::Schema* schema) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.schema_);
  }
  if (schema) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(schema));
    if (message_arena != submessage_arena) {
      schema = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, schema, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.schema_ = schema;
  // @@protoc_insertion_point(field_set_allocated:manifest_proto.Manifest.schema)
}

// repeated .manifest_proto.Fragment scalar_fragments = 4;
inline int Manifest::_internal_scalar_fragments_size() const {
  return _impl_.scalar_fragments_.size();
}
inline int Manifest::scalar_fragments_size() const {
  return _internal_scalar_fragments_size();
}
inline void Manifest::clear_scalar_fragments() {
  _impl_.scalar_fragments_.Clear();
}
inline ::manifest_proto::Fragment* Manifest::mutable_scalar_fragments(int index) {
  // @@protoc_insertion_point(field_mutable:manifest_proto.Manifest.scalar_fragments)
  return _impl_.scalar_fragments_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::manifest_proto::Fragment >*
Manifest::mutable_scalar_fragments() {
  // @@protoc_insertion_point(field_mutable_list:manifest_proto.Manifest.scalar_fragments)
  return &_impl_.scalar_fragments_;
}
inline const ::manifest_proto::Fragment& Manifest::_internal_scalar_fragments(int index) const {
  return _impl_.scalar_fragments_.Get(index);
}
inline const ::manifest_proto::Fragment& Manifest::scalar_fragments(int index) const {
  // @@protoc_insertion_point(field_get:manifest_proto.Manifest.scalar_fragments)
  return _internal_scalar_fragments(index);
}
inline ::manifest_proto::Fragment* Manifest::_internal_add_scalar_fragments() {
  return _impl_.scalar_fragments_.Add();
}
inline ::manifest_proto::Fragment* Manifest::add_scalar_fragments() {
  ::manifest_proto::Fragment* _add = _internal_add_scalar_fragments();
  // @@protoc_insertion_point(field_add:manifest_proto.Manifest.scalar_fragments)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::manifest_proto::Fragment >&
Manifest::scalar_fragments() const {
  // @@protoc_insertion_point(field_list:manifest_proto.Manifest.scalar_fragments)
  return _impl_.scalar_fragments_;
}

// repeated .manifest_proto.Fragment vector_fragments = 5;
inline int Manifest::_internal_vector_fragments_size() const {
  return _impl_.vector_fragments_.size();
}
inline int Manifest::vector_fragments_size() const {
  return _internal_vector_fragments_size();
}
inline void Manifest::clear_vector_fragments() {
  _impl_.vector_fragments_.Clear();
}
inline ::manifest_proto::Fragment* Manifest::mutable_vector_fragments(int index) {
  // @@protoc_insertion_point(field_mutable:manifest_proto.Manifest.vector_fragments)
  return _impl_.vector_fragments_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::manifest_proto::Fragment >*
Manifest::mutable_vector_fragments() {
  // @@protoc_insertion_point(field_mutable_list:manifest_proto.Manifest.vector_fragments)
  return &_impl_.vector_fragments_;
}
inline const ::manifest_proto::Fragment& Manifest::_internal_vector_fragments(int index) const {
  return _impl_.vector_fragments_.Get(index);
}
inline const ::manifest_proto::Fragment& Manifest::vector_fragments(int index) const {
  // @@protoc_insertion_point(field_get:manifest_proto.Manifest.vector_fragments)
  return _internal_vector_fragments(index);
}
inline ::manifest_proto::Fragment* Manifest::_internal_add_vector_fragments() {
  return _impl_.vector_fragments_.Add();
}
inline ::manifest_proto::Fragment* Manifest::add_vector_fragments() {
  ::manifest_proto::Fragment* _add = _internal_add_vector_fragments();
  // @@protoc_insertion_point(field_add:manifest_proto.Manifest.vector_fragments)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::manifest_proto::Fragment >&
Manifest::vector_fragments() const {
  // @@protoc_insertion_point(field_list:manifest_proto.Manifest.vector_fragments)
  return _impl_.vector_fragments_;
}

// repeated .manifest_proto.Fragment delete_fragments = 6;
inline int Manifest::_internal_delete_fragments_size() const {
  return _impl_.delete_fragments_.size();
}
inline int Manifest::delete_fragments_size() const {
  return _internal_delete_fragments_size();
}
inline void Manifest::clear_delete_fragments() {
  _impl_.delete_fragments_.Clear();
}
inline ::manifest_proto::Fragment* Manifest::mutable_delete_fragments(int index) {
  // @@protoc_insertion_point(field_mutable:manifest_proto.Manifest.delete_fragments)
  return _impl_.delete_fragments_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::manifest_proto::Fragment >*
Manifest::mutable_delete_fragments() {
  // @@protoc_insertion_point(field_mutable_list:manifest_proto.Manifest.delete_fragments)
  return &_impl_.delete_fragments_;
}
inline const ::manifest_proto::Fragment& Manifest::_internal_delete_fragments(int index) const {
  return _impl_.delete_fragments_.Get(index);
}
inline const ::manifest_proto::Fragment& Manifest::delete_fragments(int index) const {
  // @@protoc_insertion_point(field_get:manifest_proto.Manifest.delete_fragments)
  return _internal_delete_fragments(index);
}
inline ::manifest_proto::Fragment* Manifest::_internal_add_delete_fragments() {
  return _impl_.delete_fragments_.Add();
}
inline ::manifest_proto::Fragment* Manifest::add_delete_fragments() {
  ::manifest_proto::Fragment* _add = _internal_add_delete_fragments();
  // @@protoc_insertion_point(field_add:manifest_proto.Manifest.delete_fragments)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::manifest_proto::Fragment >&
Manifest::delete_fragments() const {
  // @@protoc_insertion_point(field_list:manifest_proto.Manifest.delete_fragments)
  return _impl_.delete_fragments_;
}

// repeated .manifest_proto.Blob blobs = 7;
inline int Manifest::_internal_blobs_size() const {
  return _impl_.blobs_.size();
}
inline int Manifest::blobs_size() const {
  return _internal_blobs_size();
}
inline void Manifest::clear_blobs() {
  _impl_.blobs_.Clear();
}
inline ::manifest_proto::Blob* Manifest::mutable_blobs(int index) {
  // @@protoc_insertion_point(field_mutable:manifest_proto.Manifest.blobs)
  return _impl_.blobs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::manifest_proto::Blob >*
Manifest::mutable_blobs() {
  // @@protoc_insertion_point(field_mutable_list:manifest_proto.Manifest.blobs)
  return &_impl_.blobs_;
}
inline const ::manifest_proto::Blob& Manifest::_internal_blobs(int index) const {
  return _impl_.blobs_.Get(index);
}
inline const ::manifest_proto::Blob& Manifest::blobs(int index) const {
  // @@protoc_insertion_point(field_get:manifest_proto.Manifest.blobs)
  return _internal_blobs(index);
}
inline ::manifest_proto::Blob* Manifest::_internal_add_blobs() {
  return _impl_.blobs_.Add();
}
inline ::manifest_proto::Blob* Manifest::add_blobs() {
  ::manifest_proto::Blob* _add = _internal_add_blobs();
  // @@protoc_insertion_point(field_add:manifest_proto.Manifest.blobs)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::manifest_proto::Blob >&
Manifest::blobs() const {
  // @@protoc_insertion_point(field_list:manifest_proto.Manifest.blobs)
  return _impl_.blobs_;
}

// -------------------------------------------------------------------

// Fragment

// int64 id = 1;
inline void Fragment::clear_id() {
  _impl_.id_ = int64_t{0};
}
inline int64_t Fragment::_internal_id() const {
  return _impl_.id_;
}
inline int64_t Fragment::id() const {
  // @@protoc_insertion_point(field_get:manifest_proto.Fragment.id)
  return _internal_id();
}
inline void Fragment::_internal_set_id(int64_t value) {
  
  _impl_.id_ = value;
}
inline void Fragment::set_id(int64_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:manifest_proto.Fragment.id)
}

// repeated string files = 2;
inline int Fragment::_internal_files_size() const {
  return _impl_.files_.size();
}
inline int Fragment::files_size() const {
  return _internal_files_size();
}
inline void Fragment::clear_files() {
  _impl_.files_.Clear();
}
inline std::string* Fragment::add_files() {
  std::string* _s = _internal_add_files();
  // @@protoc_insertion_point(field_add_mutable:manifest_proto.Fragment.files)
  return _s;
}
inline const std::string& Fragment::_internal_files(int index) const {
  return _impl_.files_.Get(index);
}
inline const std::string& Fragment::files(int index) const {
  // @@protoc_insertion_point(field_get:manifest_proto.Fragment.files)
  return _internal_files(index);
}
inline std::string* Fragment::mutable_files(int index) {
  // @@protoc_insertion_point(field_mutable:manifest_proto.Fragment.files)
  return _impl_.files_.Mutable(index);
}
inline void Fragment::set_files(int index, const std::string& value) {
  _impl_.files_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:manifest_proto.Fragment.files)
}
inline void Fragment::set_files(int index, std::string&& value) {
  _impl_.files_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:manifest_proto.Fragment.files)
}
inline void Fragment::set_files(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.files_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:manifest_proto.Fragment.files)
}
inline void Fragment::set_files(int index, const char* value, size_t size) {
  _impl_.files_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:manifest_proto.Fragment.files)
}
inline std::string* Fragment::_internal_add_files() {
  return _impl_.files_.Add();
}
inline void Fragment::add_files(const std::string& value) {
  _impl_.files_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:manifest_proto.Fragment.files)
}
inline void Fragment::add_files(std::string&& value) {
  _impl_.files_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:manifest_proto.Fragment.files)
}
inline void Fragment::add_files(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.files_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:manifest_proto.Fragment.files)
}
inline void Fragment::add_files(const char* value, size_t size) {
  _impl_.files_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:manifest_proto.Fragment.files)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
Fragment::files() const {
  // @@protoc_insertion_point(field_list:manifest_proto.Fragment.files)
  return _impl_.files_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
Fragment::mutable_files() {
  // @@protoc_insertion_point(field_mutable_list:manifest_proto.Fragment.files)
  return &_impl_.files_;
}

// -------------------------------------------------------------------

// Blob

// string name = 1;
inline void Blob::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& Blob::name() const {
  // @@protoc_insertion_point(field_get:manifest_proto.Blob.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Blob::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:manifest_proto.Blob.name)
}
inline std::string* Blob::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:manifest_proto.Blob.name)
  return _s;
}
inline const std::string& Blob::_internal_name() const {
  return _impl_.name_.Get();
}
inline void Blob::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* Blob::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* Blob::release_name() {
  // @@protoc_insertion_point(field_release:manifest_proto.Blob.name)
  return _impl_.name_.Release();
}
inline void Blob::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:manifest_proto.Blob.name)
}

// int64 size = 2;
inline void Blob::clear_size() {
  _impl_.size_ = int64_t{0};
}
inline int64_t Blob::_internal_size() const {
  return _impl_.size_;
}
inline int64_t Blob::size() const {
  // @@protoc_insertion_point(field_get:manifest_proto.Blob.size)
  return _internal_size();
}
inline void Blob::_internal_set_size(int64_t value) {
  
  _impl_.size_ = value;
}
inline void Blob::set_size(int64_t value) {
  _internal_set_size(value);
  // @@protoc_insertion_point(field_set:manifest_proto.Blob.size)
}

// string file = 3;
inline void Blob::clear_file() {
  _impl_.file_.ClearToEmpty();
}
inline const std::string& Blob::file() const {
  // @@protoc_insertion_point(field_get:manifest_proto.Blob.file)
  return _internal_file();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Blob::set_file(ArgT0&& arg0, ArgT... args) {
 
 _impl_.file_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:manifest_proto.Blob.file)
}
inline std::string* Blob::mutable_file() {
  std::string* _s = _internal_mutable_file();
  // @@protoc_insertion_point(field_mutable:manifest_proto.Blob.file)
  return _s;
}
inline const std::string& Blob::_internal_file() const {
  return _impl_.file_.Get();
}
inline void Blob::_internal_set_file(const std::string& value) {
  
  _impl_.file_.Set(value, GetArenaForAllocation());
}
inline std::string* Blob::_internal_mutable_file() {
  
  return _impl_.file_.Mutable(GetArenaForAllocation());
}
inline std::string* Blob::release_file() {
  // @@protoc_insertion_point(field_release:manifest_proto.Blob.file)
  return _impl_.file_.Release();
}
inline void Blob::set_allocated_file(std::string* file) {
  if (file != nullptr) {
    
  } else {
    
  }
  _impl_.file_.SetAllocated(file, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.file_.IsDefault()) {
    _impl_.file_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:manifest_proto.Blob.file)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace manifest_proto

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_manifest_2eproto
