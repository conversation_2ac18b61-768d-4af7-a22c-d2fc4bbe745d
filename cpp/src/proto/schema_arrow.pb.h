// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: schema_arrow.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_schema_5farrow_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_schema_5farrow_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021004 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_schema_5farrow_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_schema_5farrow_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_schema_5farrow_2eproto;
namespace schema_proto {
class ArrowSchema;
struct ArrowSchemaDefaultTypeInternal;
extern ArrowSchemaDefaultTypeInternal _ArrowSchema_default_instance_;
class DataType;
struct DataTypeDefaultTypeInternal;
extern DataTypeDefaultTypeInternal _DataType_default_instance_;
class DictionaryType;
struct DictionaryTypeDefaultTypeInternal;
extern DictionaryTypeDefaultTypeInternal _DictionaryType_default_instance_;
class Field;
struct FieldDefaultTypeInternal;
extern FieldDefaultTypeInternal _Field_default_instance_;
class FixedSizeBinaryType;
struct FixedSizeBinaryTypeDefaultTypeInternal;
extern FixedSizeBinaryTypeDefaultTypeInternal _FixedSizeBinaryType_default_instance_;
class FixedSizeListType;
struct FixedSizeListTypeDefaultTypeInternal;
extern FixedSizeListTypeDefaultTypeInternal _FixedSizeListType_default_instance_;
class KeyValueMetadata;
struct KeyValueMetadataDefaultTypeInternal;
extern KeyValueMetadataDefaultTypeInternal _KeyValueMetadata_default_instance_;
class MapType;
struct MapTypeDefaultTypeInternal;
extern MapTypeDefaultTypeInternal _MapType_default_instance_;
class Schema;
struct SchemaDefaultTypeInternal;
extern SchemaDefaultTypeInternal _Schema_default_instance_;
class SchemaOptions;
struct SchemaOptionsDefaultTypeInternal;
extern SchemaOptionsDefaultTypeInternal _SchemaOptions_default_instance_;
}  // namespace schema_proto
PROTOBUF_NAMESPACE_OPEN
template<> ::schema_proto::ArrowSchema* Arena::CreateMaybeMessage<::schema_proto::ArrowSchema>(Arena*);
template<> ::schema_proto::DataType* Arena::CreateMaybeMessage<::schema_proto::DataType>(Arena*);
template<> ::schema_proto::DictionaryType* Arena::CreateMaybeMessage<::schema_proto::DictionaryType>(Arena*);
template<> ::schema_proto::Field* Arena::CreateMaybeMessage<::schema_proto::Field>(Arena*);
template<> ::schema_proto::FixedSizeBinaryType* Arena::CreateMaybeMessage<::schema_proto::FixedSizeBinaryType>(Arena*);
template<> ::schema_proto::FixedSizeListType* Arena::CreateMaybeMessage<::schema_proto::FixedSizeListType>(Arena*);
template<> ::schema_proto::KeyValueMetadata* Arena::CreateMaybeMessage<::schema_proto::KeyValueMetadata>(Arena*);
template<> ::schema_proto::MapType* Arena::CreateMaybeMessage<::schema_proto::MapType>(Arena*);
template<> ::schema_proto::Schema* Arena::CreateMaybeMessage<::schema_proto::Schema>(Arena*);
template<> ::schema_proto::SchemaOptions* Arena::CreateMaybeMessage<::schema_proto::SchemaOptions>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace schema_proto {

enum LogicType : int {
  NA = 0,
  BOOL = 1,
  UINT8 = 2,
  INT8 = 3,
  UINT16 = 4,
  INT16 = 5,
  UINT32 = 6,
  INT32 = 7,
  UINT64 = 8,
  INT64 = 9,
  HALF_FLOAT = 10,
  FLOAT = 11,
  DOUBLE = 12,
  STRING = 13,
  BINARY = 14,
  FIXED_SIZE_BINARY = 15,
  LIST = 25,
  STRUCT = 26,
  DICTIONARY = 29,
  MAP = 30,
  FIXED_SIZE_LIST = 32,
  MAX_ID = 39,
  LogicType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  LogicType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool LogicType_IsValid(int value);
constexpr LogicType LogicType_MIN = NA;
constexpr LogicType LogicType_MAX = MAX_ID;
constexpr int LogicType_ARRAYSIZE = LogicType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* LogicType_descriptor();
template<typename T>
inline const std::string& LogicType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, LogicType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function LogicType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    LogicType_descriptor(), enum_t_value);
}
inline bool LogicType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, LogicType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<LogicType>(
    LogicType_descriptor(), name, value);
}
enum Endianness : int {
  Little = 0,
  Big = 1,
  Endianness_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  Endianness_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool Endianness_IsValid(int value);
constexpr Endianness Endianness_MIN = Little;
constexpr Endianness Endianness_MAX = Big;
constexpr int Endianness_ARRAYSIZE = Endianness_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Endianness_descriptor();
template<typename T>
inline const std::string& Endianness_Name(T enum_t_value) {
  static_assert(::std::is_same<T, Endianness>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function Endianness_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    Endianness_descriptor(), enum_t_value);
}
inline bool Endianness_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, Endianness* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<Endianness>(
    Endianness_descriptor(), name, value);
}
// ===================================================================

class FixedSizeBinaryType final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:schema_proto.FixedSizeBinaryType) */ {
 public:
  inline FixedSizeBinaryType() : FixedSizeBinaryType(nullptr) {}
  ~FixedSizeBinaryType() override;
  explicit PROTOBUF_CONSTEXPR FixedSizeBinaryType(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FixedSizeBinaryType(const FixedSizeBinaryType& from);
  FixedSizeBinaryType(FixedSizeBinaryType&& from) noexcept
    : FixedSizeBinaryType() {
    *this = ::std::move(from);
  }

  inline FixedSizeBinaryType& operator=(const FixedSizeBinaryType& from) {
    CopyFrom(from);
    return *this;
  }
  inline FixedSizeBinaryType& operator=(FixedSizeBinaryType&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FixedSizeBinaryType& default_instance() {
    return *internal_default_instance();
  }
  static inline const FixedSizeBinaryType* internal_default_instance() {
    return reinterpret_cast<const FixedSizeBinaryType*>(
               &_FixedSizeBinaryType_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(FixedSizeBinaryType& a, FixedSizeBinaryType& b) {
    a.Swap(&b);
  }
  inline void Swap(FixedSizeBinaryType* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FixedSizeBinaryType* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FixedSizeBinaryType* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FixedSizeBinaryType>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FixedSizeBinaryType& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const FixedSizeBinaryType& from) {
    FixedSizeBinaryType::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FixedSizeBinaryType* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "schema_proto.FixedSizeBinaryType";
  }
  protected:
  explicit FixedSizeBinaryType(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kByteWidthFieldNumber = 1,
  };
  // int32 byte_width = 1;
  void clear_byte_width();
  int32_t byte_width() const;
  void set_byte_width(int32_t value);
  private:
  int32_t _internal_byte_width() const;
  void _internal_set_byte_width(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:schema_proto.FixedSizeBinaryType)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int32_t byte_width_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_schema_5farrow_2eproto;
};
// -------------------------------------------------------------------

class FixedSizeListType final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:schema_proto.FixedSizeListType) */ {
 public:
  inline FixedSizeListType() : FixedSizeListType(nullptr) {}
  ~FixedSizeListType() override;
  explicit PROTOBUF_CONSTEXPR FixedSizeListType(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FixedSizeListType(const FixedSizeListType& from);
  FixedSizeListType(FixedSizeListType&& from) noexcept
    : FixedSizeListType() {
    *this = ::std::move(from);
  }

  inline FixedSizeListType& operator=(const FixedSizeListType& from) {
    CopyFrom(from);
    return *this;
  }
  inline FixedSizeListType& operator=(FixedSizeListType&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FixedSizeListType& default_instance() {
    return *internal_default_instance();
  }
  static inline const FixedSizeListType* internal_default_instance() {
    return reinterpret_cast<const FixedSizeListType*>(
               &_FixedSizeListType_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(FixedSizeListType& a, FixedSizeListType& b) {
    a.Swap(&b);
  }
  inline void Swap(FixedSizeListType* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FixedSizeListType* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FixedSizeListType* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FixedSizeListType>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FixedSizeListType& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const FixedSizeListType& from) {
    FixedSizeListType::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FixedSizeListType* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "schema_proto.FixedSizeListType";
  }
  protected:
  explicit FixedSizeListType(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kListSizeFieldNumber = 1,
  };
  // int32 list_size = 1;
  void clear_list_size();
  int32_t list_size() const;
  void set_list_size(int32_t value);
  private:
  int32_t _internal_list_size() const;
  void _internal_set_list_size(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:schema_proto.FixedSizeListType)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int32_t list_size_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_schema_5farrow_2eproto;
};
// -------------------------------------------------------------------

class DictionaryType final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:schema_proto.DictionaryType) */ {
 public:
  inline DictionaryType() : DictionaryType(nullptr) {}
  ~DictionaryType() override;
  explicit PROTOBUF_CONSTEXPR DictionaryType(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DictionaryType(const DictionaryType& from);
  DictionaryType(DictionaryType&& from) noexcept
    : DictionaryType() {
    *this = ::std::move(from);
  }

  inline DictionaryType& operator=(const DictionaryType& from) {
    CopyFrom(from);
    return *this;
  }
  inline DictionaryType& operator=(DictionaryType&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DictionaryType& default_instance() {
    return *internal_default_instance();
  }
  static inline const DictionaryType* internal_default_instance() {
    return reinterpret_cast<const DictionaryType*>(
               &_DictionaryType_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(DictionaryType& a, DictionaryType& b) {
    a.Swap(&b);
  }
  inline void Swap(DictionaryType* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DictionaryType* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DictionaryType* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DictionaryType>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DictionaryType& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DictionaryType& from) {
    DictionaryType::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DictionaryType* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "schema_proto.DictionaryType";
  }
  protected:
  explicit DictionaryType(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIndexTypeFieldNumber = 1,
    kValueTypeFieldNumber = 2,
    kOrderedFieldNumber = 3,
  };
  // .schema_proto.DataType index_type = 1;
  bool has_index_type() const;
  private:
  bool _internal_has_index_type() const;
  public:
  void clear_index_type();
  const ::schema_proto::DataType& index_type() const;
  PROTOBUF_NODISCARD ::schema_proto::DataType* release_index_type();
  ::schema_proto::DataType* mutable_index_type();
  void set_allocated_index_type(::schema_proto::DataType* index_type);
  private:
  const ::schema_proto::DataType& _internal_index_type() const;
  ::schema_proto::DataType* _internal_mutable_index_type();
  public:
  void unsafe_arena_set_allocated_index_type(
      ::schema_proto::DataType* index_type);
  ::schema_proto::DataType* unsafe_arena_release_index_type();

  // .schema_proto.DataType value_type = 2;
  bool has_value_type() const;
  private:
  bool _internal_has_value_type() const;
  public:
  void clear_value_type();
  const ::schema_proto::DataType& value_type() const;
  PROTOBUF_NODISCARD ::schema_proto::DataType* release_value_type();
  ::schema_proto::DataType* mutable_value_type();
  void set_allocated_value_type(::schema_proto::DataType* value_type);
  private:
  const ::schema_proto::DataType& _internal_value_type() const;
  ::schema_proto::DataType* _internal_mutable_value_type();
  public:
  void unsafe_arena_set_allocated_value_type(
      ::schema_proto::DataType* value_type);
  ::schema_proto::DataType* unsafe_arena_release_value_type();

  // bool ordered = 3;
  void clear_ordered();
  bool ordered() const;
  void set_ordered(bool value);
  private:
  bool _internal_ordered() const;
  void _internal_set_ordered(bool value);
  public:

  // @@protoc_insertion_point(class_scope:schema_proto.DictionaryType)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::schema_proto::DataType* index_type_;
    ::schema_proto::DataType* value_type_;
    bool ordered_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_schema_5farrow_2eproto;
};
// -------------------------------------------------------------------

class MapType final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:schema_proto.MapType) */ {
 public:
  inline MapType() : MapType(nullptr) {}
  ~MapType() override;
  explicit PROTOBUF_CONSTEXPR MapType(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MapType(const MapType& from);
  MapType(MapType&& from) noexcept
    : MapType() {
    *this = ::std::move(from);
  }

  inline MapType& operator=(const MapType& from) {
    CopyFrom(from);
    return *this;
  }
  inline MapType& operator=(MapType&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MapType& default_instance() {
    return *internal_default_instance();
  }
  static inline const MapType* internal_default_instance() {
    return reinterpret_cast<const MapType*>(
               &_MapType_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(MapType& a, MapType& b) {
    a.Swap(&b);
  }
  inline void Swap(MapType* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MapType* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MapType* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MapType>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MapType& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MapType& from) {
    MapType::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MapType* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "schema_proto.MapType";
  }
  protected:
  explicit MapType(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKeysSortedFieldNumber = 1,
  };
  // bool keys_sorted = 1;
  void clear_keys_sorted();
  bool keys_sorted() const;
  void set_keys_sorted(bool value);
  private:
  bool _internal_keys_sorted() const;
  void _internal_set_keys_sorted(bool value);
  public:

  // @@protoc_insertion_point(class_scope:schema_proto.MapType)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    bool keys_sorted_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_schema_5farrow_2eproto;
};
// -------------------------------------------------------------------

class DataType final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:schema_proto.DataType) */ {
 public:
  inline DataType() : DataType(nullptr) {}
  ~DataType() override;
  explicit PROTOBUF_CONSTEXPR DataType(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DataType(const DataType& from);
  DataType(DataType&& from) noexcept
    : DataType() {
    *this = ::std::move(from);
  }

  inline DataType& operator=(const DataType& from) {
    CopyFrom(from);
    return *this;
  }
  inline DataType& operator=(DataType&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DataType& default_instance() {
    return *internal_default_instance();
  }
  enum TypeRelatedValuesCase {
    kFixedSizeBinaryType = 1,
    kFixedSizeListType = 2,
    kDictionaryType = 3,
    kMapType = 4,
    TYPE_RELATED_VALUES_NOT_SET = 0,
  };

  static inline const DataType* internal_default_instance() {
    return reinterpret_cast<const DataType*>(
               &_DataType_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(DataType& a, DataType& b) {
    a.Swap(&b);
  }
  inline void Swap(DataType* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DataType* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DataType* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DataType>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DataType& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DataType& from) {
    DataType::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DataType* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "schema_proto.DataType";
  }
  protected:
  explicit DataType(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kChildrenFieldNumber = 101,
    kLogicTypeFieldNumber = 100,
    kFixedSizeBinaryTypeFieldNumber = 1,
    kFixedSizeListTypeFieldNumber = 2,
    kDictionaryTypeFieldNumber = 3,
    kMapTypeFieldNumber = 4,
  };
  // repeated .schema_proto.Field children = 101;
  int children_size() const;
  private:
  int _internal_children_size() const;
  public:
  void clear_children();
  ::schema_proto::Field* mutable_children(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::schema_proto::Field >*
      mutable_children();
  private:
  const ::schema_proto::Field& _internal_children(int index) const;
  ::schema_proto::Field* _internal_add_children();
  public:
  const ::schema_proto::Field& children(int index) const;
  ::schema_proto::Field* add_children();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::schema_proto::Field >&
      children() const;

  // .schema_proto.LogicType logic_type = 100;
  void clear_logic_type();
  ::schema_proto::LogicType logic_type() const;
  void set_logic_type(::schema_proto::LogicType value);
  private:
  ::schema_proto::LogicType _internal_logic_type() const;
  void _internal_set_logic_type(::schema_proto::LogicType value);
  public:

  // .schema_proto.FixedSizeBinaryType fixed_size_binary_type = 1;
  bool has_fixed_size_binary_type() const;
  private:
  bool _internal_has_fixed_size_binary_type() const;
  public:
  void clear_fixed_size_binary_type();
  const ::schema_proto::FixedSizeBinaryType& fixed_size_binary_type() const;
  PROTOBUF_NODISCARD ::schema_proto::FixedSizeBinaryType* release_fixed_size_binary_type();
  ::schema_proto::FixedSizeBinaryType* mutable_fixed_size_binary_type();
  void set_allocated_fixed_size_binary_type(::schema_proto::FixedSizeBinaryType* fixed_size_binary_type);
  private:
  const ::schema_proto::FixedSizeBinaryType& _internal_fixed_size_binary_type() const;
  ::schema_proto::FixedSizeBinaryType* _internal_mutable_fixed_size_binary_type();
  public:
  void unsafe_arena_set_allocated_fixed_size_binary_type(
      ::schema_proto::FixedSizeBinaryType* fixed_size_binary_type);
  ::schema_proto::FixedSizeBinaryType* unsafe_arena_release_fixed_size_binary_type();

  // .schema_proto.FixedSizeListType fixed_size_list_type = 2;
  bool has_fixed_size_list_type() const;
  private:
  bool _internal_has_fixed_size_list_type() const;
  public:
  void clear_fixed_size_list_type();
  const ::schema_proto::FixedSizeListType& fixed_size_list_type() const;
  PROTOBUF_NODISCARD ::schema_proto::FixedSizeListType* release_fixed_size_list_type();
  ::schema_proto::FixedSizeListType* mutable_fixed_size_list_type();
  void set_allocated_fixed_size_list_type(::schema_proto::FixedSizeListType* fixed_size_list_type);
  private:
  const ::schema_proto::FixedSizeListType& _internal_fixed_size_list_type() const;
  ::schema_proto::FixedSizeListType* _internal_mutable_fixed_size_list_type();
  public:
  void unsafe_arena_set_allocated_fixed_size_list_type(
      ::schema_proto::FixedSizeListType* fixed_size_list_type);
  ::schema_proto::FixedSizeListType* unsafe_arena_release_fixed_size_list_type();

  // .schema_proto.DictionaryType dictionary_type = 3;
  bool has_dictionary_type() const;
  private:
  bool _internal_has_dictionary_type() const;
  public:
  void clear_dictionary_type();
  const ::schema_proto::DictionaryType& dictionary_type() const;
  PROTOBUF_NODISCARD ::schema_proto::DictionaryType* release_dictionary_type();
  ::schema_proto::DictionaryType* mutable_dictionary_type();
  void set_allocated_dictionary_type(::schema_proto::DictionaryType* dictionary_type);
  private:
  const ::schema_proto::DictionaryType& _internal_dictionary_type() const;
  ::schema_proto::DictionaryType* _internal_mutable_dictionary_type();
  public:
  void unsafe_arena_set_allocated_dictionary_type(
      ::schema_proto::DictionaryType* dictionary_type);
  ::schema_proto::DictionaryType* unsafe_arena_release_dictionary_type();

  // .schema_proto.MapType map_type = 4;
  bool has_map_type() const;
  private:
  bool _internal_has_map_type() const;
  public:
  void clear_map_type();
  const ::schema_proto::MapType& map_type() const;
  PROTOBUF_NODISCARD ::schema_proto::MapType* release_map_type();
  ::schema_proto::MapType* mutable_map_type();
  void set_allocated_map_type(::schema_proto::MapType* map_type);
  private:
  const ::schema_proto::MapType& _internal_map_type() const;
  ::schema_proto::MapType* _internal_mutable_map_type();
  public:
  void unsafe_arena_set_allocated_map_type(
      ::schema_proto::MapType* map_type);
  ::schema_proto::MapType* unsafe_arena_release_map_type();

  void clear_type_related_values();
  TypeRelatedValuesCase type_related_values_case() const;
  // @@protoc_insertion_point(class_scope:schema_proto.DataType)
 private:
  class _Internal;
  void set_has_fixed_size_binary_type();
  void set_has_fixed_size_list_type();
  void set_has_dictionary_type();
  void set_has_map_type();

  inline bool has_type_related_values() const;
  inline void clear_has_type_related_values();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::schema_proto::Field > children_;
    int logic_type_;
    union TypeRelatedValuesUnion {
      constexpr TypeRelatedValuesUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::schema_proto::FixedSizeBinaryType* fixed_size_binary_type_;
      ::schema_proto::FixedSizeListType* fixed_size_list_type_;
      ::schema_proto::DictionaryType* dictionary_type_;
      ::schema_proto::MapType* map_type_;
    } type_related_values_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_schema_5farrow_2eproto;
};
// -------------------------------------------------------------------

class KeyValueMetadata final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:schema_proto.KeyValueMetadata) */ {
 public:
  inline KeyValueMetadata() : KeyValueMetadata(nullptr) {}
  ~KeyValueMetadata() override;
  explicit PROTOBUF_CONSTEXPR KeyValueMetadata(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  KeyValueMetadata(const KeyValueMetadata& from);
  KeyValueMetadata(KeyValueMetadata&& from) noexcept
    : KeyValueMetadata() {
    *this = ::std::move(from);
  }

  inline KeyValueMetadata& operator=(const KeyValueMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline KeyValueMetadata& operator=(KeyValueMetadata&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const KeyValueMetadata& default_instance() {
    return *internal_default_instance();
  }
  static inline const KeyValueMetadata* internal_default_instance() {
    return reinterpret_cast<const KeyValueMetadata*>(
               &_KeyValueMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(KeyValueMetadata& a, KeyValueMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(KeyValueMetadata* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(KeyValueMetadata* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  KeyValueMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<KeyValueMetadata>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const KeyValueMetadata& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const KeyValueMetadata& from) {
    KeyValueMetadata::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(KeyValueMetadata* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "schema_proto.KeyValueMetadata";
  }
  protected:
  explicit KeyValueMetadata(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKeysFieldNumber = 1,
    kValuesFieldNumber = 2,
  };
  // repeated string keys = 1;
  int keys_size() const;
  private:
  int _internal_keys_size() const;
  public:
  void clear_keys();
  const std::string& keys(int index) const;
  std::string* mutable_keys(int index);
  void set_keys(int index, const std::string& value);
  void set_keys(int index, std::string&& value);
  void set_keys(int index, const char* value);
  void set_keys(int index, const char* value, size_t size);
  std::string* add_keys();
  void add_keys(const std::string& value);
  void add_keys(std::string&& value);
  void add_keys(const char* value);
  void add_keys(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& keys() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_keys();
  private:
  const std::string& _internal_keys(int index) const;
  std::string* _internal_add_keys();
  public:

  // repeated string values = 2;
  int values_size() const;
  private:
  int _internal_values_size() const;
  public:
  void clear_values();
  const std::string& values(int index) const;
  std::string* mutable_values(int index);
  void set_values(int index, const std::string& value);
  void set_values(int index, std::string&& value);
  void set_values(int index, const char* value);
  void set_values(int index, const char* value, size_t size);
  std::string* add_values();
  void add_values(const std::string& value);
  void add_values(std::string&& value);
  void add_values(const char* value);
  void add_values(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& values() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_values();
  private:
  const std::string& _internal_values(int index) const;
  std::string* _internal_add_values();
  public:

  // @@protoc_insertion_point(class_scope:schema_proto.KeyValueMetadata)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> keys_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> values_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_schema_5farrow_2eproto;
};
// -------------------------------------------------------------------

class Field final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:schema_proto.Field) */ {
 public:
  inline Field() : Field(nullptr) {}
  ~Field() override;
  explicit PROTOBUF_CONSTEXPR Field(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Field(const Field& from);
  Field(Field&& from) noexcept
    : Field() {
    *this = ::std::move(from);
  }

  inline Field& operator=(const Field& from) {
    CopyFrom(from);
    return *this;
  }
  inline Field& operator=(Field&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Field& default_instance() {
    return *internal_default_instance();
  }
  static inline const Field* internal_default_instance() {
    return reinterpret_cast<const Field*>(
               &_Field_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(Field& a, Field& b) {
    a.Swap(&b);
  }
  inline void Swap(Field* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Field* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Field* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Field>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Field& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Field& from) {
    Field::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Field* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "schema_proto.Field";
  }
  protected:
  explicit Field(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kDataTypeFieldNumber = 3,
    kMetadataFieldNumber = 4,
    kNullableFieldNumber = 2,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .schema_proto.DataType data_type = 3;
  bool has_data_type() const;
  private:
  bool _internal_has_data_type() const;
  public:
  void clear_data_type();
  const ::schema_proto::DataType& data_type() const;
  PROTOBUF_NODISCARD ::schema_proto::DataType* release_data_type();
  ::schema_proto::DataType* mutable_data_type();
  void set_allocated_data_type(::schema_proto::DataType* data_type);
  private:
  const ::schema_proto::DataType& _internal_data_type() const;
  ::schema_proto::DataType* _internal_mutable_data_type();
  public:
  void unsafe_arena_set_allocated_data_type(
      ::schema_proto::DataType* data_type);
  ::schema_proto::DataType* unsafe_arena_release_data_type();

  // .schema_proto.KeyValueMetadata metadata = 4;
  bool has_metadata() const;
  private:
  bool _internal_has_metadata() const;
  public:
  void clear_metadata();
  const ::schema_proto::KeyValueMetadata& metadata() const;
  PROTOBUF_NODISCARD ::schema_proto::KeyValueMetadata* release_metadata();
  ::schema_proto::KeyValueMetadata* mutable_metadata();
  void set_allocated_metadata(::schema_proto::KeyValueMetadata* metadata);
  private:
  const ::schema_proto::KeyValueMetadata& _internal_metadata() const;
  ::schema_proto::KeyValueMetadata* _internal_mutable_metadata();
  public:
  void unsafe_arena_set_allocated_metadata(
      ::schema_proto::KeyValueMetadata* metadata);
  ::schema_proto::KeyValueMetadata* unsafe_arena_release_metadata();

  // bool nullable = 2;
  void clear_nullable();
  bool nullable() const;
  void set_nullable(bool value);
  private:
  bool _internal_nullable() const;
  void _internal_set_nullable(bool value);
  public:

  // @@protoc_insertion_point(class_scope:schema_proto.Field)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::schema_proto::DataType* data_type_;
    ::schema_proto::KeyValueMetadata* metadata_;
    bool nullable_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_schema_5farrow_2eproto;
};
// -------------------------------------------------------------------

class SchemaOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:schema_proto.SchemaOptions) */ {
 public:
  inline SchemaOptions() : SchemaOptions(nullptr) {}
  ~SchemaOptions() override;
  explicit PROTOBUF_CONSTEXPR SchemaOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SchemaOptions(const SchemaOptions& from);
  SchemaOptions(SchemaOptions&& from) noexcept
    : SchemaOptions() {
    *this = ::std::move(from);
  }

  inline SchemaOptions& operator=(const SchemaOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline SchemaOptions& operator=(SchemaOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SchemaOptions& default_instance() {
    return *internal_default_instance();
  }
  static inline const SchemaOptions* internal_default_instance() {
    return reinterpret_cast<const SchemaOptions*>(
               &_SchemaOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(SchemaOptions& a, SchemaOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(SchemaOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SchemaOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SchemaOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SchemaOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SchemaOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SchemaOptions& from) {
    SchemaOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SchemaOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "schema_proto.SchemaOptions";
  }
  protected:
  explicit SchemaOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPrimaryColumnFieldNumber = 1,
    kVersionColumnFieldNumber = 2,
    kVectorColumnFieldNumber = 3,
  };
  // string primary_column = 1;
  void clear_primary_column();
  const std::string& primary_column() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_primary_column(ArgT0&& arg0, ArgT... args);
  std::string* mutable_primary_column();
  PROTOBUF_NODISCARD std::string* release_primary_column();
  void set_allocated_primary_column(std::string* primary_column);
  private:
  const std::string& _internal_primary_column() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_primary_column(const std::string& value);
  std::string* _internal_mutable_primary_column();
  public:

  // string version_column = 2;
  void clear_version_column();
  const std::string& version_column() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_version_column(ArgT0&& arg0, ArgT... args);
  std::string* mutable_version_column();
  PROTOBUF_NODISCARD std::string* release_version_column();
  void set_allocated_version_column(std::string* version_column);
  private:
  const std::string& _internal_version_column() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_version_column(const std::string& value);
  std::string* _internal_mutable_version_column();
  public:

  // string vector_column = 3;
  void clear_vector_column();
  const std::string& vector_column() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_vector_column(ArgT0&& arg0, ArgT... args);
  std::string* mutable_vector_column();
  PROTOBUF_NODISCARD std::string* release_vector_column();
  void set_allocated_vector_column(std::string* vector_column);
  private:
  const std::string& _internal_vector_column() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_vector_column(const std::string& value);
  std::string* _internal_mutable_vector_column();
  public:

  // @@protoc_insertion_point(class_scope:schema_proto.SchemaOptions)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr primary_column_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr version_column_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr vector_column_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_schema_5farrow_2eproto;
};
// -------------------------------------------------------------------

class ArrowSchema final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:schema_proto.ArrowSchema) */ {
 public:
  inline ArrowSchema() : ArrowSchema(nullptr) {}
  ~ArrowSchema() override;
  explicit PROTOBUF_CONSTEXPR ArrowSchema(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ArrowSchema(const ArrowSchema& from);
  ArrowSchema(ArrowSchema&& from) noexcept
    : ArrowSchema() {
    *this = ::std::move(from);
  }

  inline ArrowSchema& operator=(const ArrowSchema& from) {
    CopyFrom(from);
    return *this;
  }
  inline ArrowSchema& operator=(ArrowSchema&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ArrowSchema& default_instance() {
    return *internal_default_instance();
  }
  static inline const ArrowSchema* internal_default_instance() {
    return reinterpret_cast<const ArrowSchema*>(
               &_ArrowSchema_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(ArrowSchema& a, ArrowSchema& b) {
    a.Swap(&b);
  }
  inline void Swap(ArrowSchema* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ArrowSchema* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ArrowSchema* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ArrowSchema>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ArrowSchema& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ArrowSchema& from) {
    ArrowSchema::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ArrowSchema* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "schema_proto.ArrowSchema";
  }
  protected:
  explicit ArrowSchema(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFieldsFieldNumber = 1,
    kMetadataFieldNumber = 3,
    kEndiannessFieldNumber = 2,
  };
  // repeated .schema_proto.Field fields = 1;
  int fields_size() const;
  private:
  int _internal_fields_size() const;
  public:
  void clear_fields();
  ::schema_proto::Field* mutable_fields(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::schema_proto::Field >*
      mutable_fields();
  private:
  const ::schema_proto::Field& _internal_fields(int index) const;
  ::schema_proto::Field* _internal_add_fields();
  public:
  const ::schema_proto::Field& fields(int index) const;
  ::schema_proto::Field* add_fields();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::schema_proto::Field >&
      fields() const;

  // .schema_proto.KeyValueMetadata metadata = 3;
  bool has_metadata() const;
  private:
  bool _internal_has_metadata() const;
  public:
  void clear_metadata();
  const ::schema_proto::KeyValueMetadata& metadata() const;
  PROTOBUF_NODISCARD ::schema_proto::KeyValueMetadata* release_metadata();
  ::schema_proto::KeyValueMetadata* mutable_metadata();
  void set_allocated_metadata(::schema_proto::KeyValueMetadata* metadata);
  private:
  const ::schema_proto::KeyValueMetadata& _internal_metadata() const;
  ::schema_proto::KeyValueMetadata* _internal_mutable_metadata();
  public:
  void unsafe_arena_set_allocated_metadata(
      ::schema_proto::KeyValueMetadata* metadata);
  ::schema_proto::KeyValueMetadata* unsafe_arena_release_metadata();

  // .schema_proto.Endianness endianness = 2;
  void clear_endianness();
  ::schema_proto::Endianness endianness() const;
  void set_endianness(::schema_proto::Endianness value);
  private:
  ::schema_proto::Endianness _internal_endianness() const;
  void _internal_set_endianness(::schema_proto::Endianness value);
  public:

  // @@protoc_insertion_point(class_scope:schema_proto.ArrowSchema)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::schema_proto::Field > fields_;
    ::schema_proto::KeyValueMetadata* metadata_;
    int endianness_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_schema_5farrow_2eproto;
};
// -------------------------------------------------------------------

class Schema final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:schema_proto.Schema) */ {
 public:
  inline Schema() : Schema(nullptr) {}
  ~Schema() override;
  explicit PROTOBUF_CONSTEXPR Schema(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Schema(const Schema& from);
  Schema(Schema&& from) noexcept
    : Schema() {
    *this = ::std::move(from);
  }

  inline Schema& operator=(const Schema& from) {
    CopyFrom(from);
    return *this;
  }
  inline Schema& operator=(Schema&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Schema& default_instance() {
    return *internal_default_instance();
  }
  static inline const Schema* internal_default_instance() {
    return reinterpret_cast<const Schema*>(
               &_Schema_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(Schema& a, Schema& b) {
    a.Swap(&b);
  }
  inline void Swap(Schema* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Schema* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Schema* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Schema>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Schema& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Schema& from) {
    Schema::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Schema* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "schema_proto.Schema";
  }
  protected:
  explicit Schema(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kArrowSchemaFieldNumber = 1,
    kSchemaOptionsFieldNumber = 2,
  };
  // .schema_proto.ArrowSchema arrow_schema = 1;
  bool has_arrow_schema() const;
  private:
  bool _internal_has_arrow_schema() const;
  public:
  void clear_arrow_schema();
  const ::schema_proto::ArrowSchema& arrow_schema() const;
  PROTOBUF_NODISCARD ::schema_proto::ArrowSchema* release_arrow_schema();
  ::schema_proto::ArrowSchema* mutable_arrow_schema();
  void set_allocated_arrow_schema(::schema_proto::ArrowSchema* arrow_schema);
  private:
  const ::schema_proto::ArrowSchema& _internal_arrow_schema() const;
  ::schema_proto::ArrowSchema* _internal_mutable_arrow_schema();
  public:
  void unsafe_arena_set_allocated_arrow_schema(
      ::schema_proto::ArrowSchema* arrow_schema);
  ::schema_proto::ArrowSchema* unsafe_arena_release_arrow_schema();

  // .schema_proto.SchemaOptions schema_options = 2;
  bool has_schema_options() const;
  private:
  bool _internal_has_schema_options() const;
  public:
  void clear_schema_options();
  const ::schema_proto::SchemaOptions& schema_options() const;
  PROTOBUF_NODISCARD ::schema_proto::SchemaOptions* release_schema_options();
  ::schema_proto::SchemaOptions* mutable_schema_options();
  void set_allocated_schema_options(::schema_proto::SchemaOptions* schema_options);
  private:
  const ::schema_proto::SchemaOptions& _internal_schema_options() const;
  ::schema_proto::SchemaOptions* _internal_mutable_schema_options();
  public:
  void unsafe_arena_set_allocated_schema_options(
      ::schema_proto::SchemaOptions* schema_options);
  ::schema_proto::SchemaOptions* unsafe_arena_release_schema_options();

  // @@protoc_insertion_point(class_scope:schema_proto.Schema)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::schema_proto::ArrowSchema* arrow_schema_;
    ::schema_proto::SchemaOptions* schema_options_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_schema_5farrow_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// FixedSizeBinaryType

// int32 byte_width = 1;
inline void FixedSizeBinaryType::clear_byte_width() {
  _impl_.byte_width_ = 0;
}
inline int32_t FixedSizeBinaryType::_internal_byte_width() const {
  return _impl_.byte_width_;
}
inline int32_t FixedSizeBinaryType::byte_width() const {
  // @@protoc_insertion_point(field_get:schema_proto.FixedSizeBinaryType.byte_width)
  return _internal_byte_width();
}
inline void FixedSizeBinaryType::_internal_set_byte_width(int32_t value) {
  
  _impl_.byte_width_ = value;
}
inline void FixedSizeBinaryType::set_byte_width(int32_t value) {
  _internal_set_byte_width(value);
  // @@protoc_insertion_point(field_set:schema_proto.FixedSizeBinaryType.byte_width)
}

// -------------------------------------------------------------------

// FixedSizeListType

// int32 list_size = 1;
inline void FixedSizeListType::clear_list_size() {
  _impl_.list_size_ = 0;
}
inline int32_t FixedSizeListType::_internal_list_size() const {
  return _impl_.list_size_;
}
inline int32_t FixedSizeListType::list_size() const {
  // @@protoc_insertion_point(field_get:schema_proto.FixedSizeListType.list_size)
  return _internal_list_size();
}
inline void FixedSizeListType::_internal_set_list_size(int32_t value) {
  
  _impl_.list_size_ = value;
}
inline void FixedSizeListType::set_list_size(int32_t value) {
  _internal_set_list_size(value);
  // @@protoc_insertion_point(field_set:schema_proto.FixedSizeListType.list_size)
}

// -------------------------------------------------------------------

// DictionaryType

// .schema_proto.DataType index_type = 1;
inline bool DictionaryType::_internal_has_index_type() const {
  return this != internal_default_instance() && _impl_.index_type_ != nullptr;
}
inline bool DictionaryType::has_index_type() const {
  return _internal_has_index_type();
}
inline void DictionaryType::clear_index_type() {
  if (GetArenaForAllocation() == nullptr && _impl_.index_type_ != nullptr) {
    delete _impl_.index_type_;
  }
  _impl_.index_type_ = nullptr;
}
inline const ::schema_proto::DataType& DictionaryType::_internal_index_type() const {
  const ::schema_proto::DataType* p = _impl_.index_type_;
  return p != nullptr ? *p : reinterpret_cast<const ::schema_proto::DataType&>(
      ::schema_proto::_DataType_default_instance_);
}
inline const ::schema_proto::DataType& DictionaryType::index_type() const {
  // @@protoc_insertion_point(field_get:schema_proto.DictionaryType.index_type)
  return _internal_index_type();
}
inline void DictionaryType::unsafe_arena_set_allocated_index_type(
    ::schema_proto::DataType* index_type) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.index_type_);
  }
  _impl_.index_type_ = index_type;
  if (index_type) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:schema_proto.DictionaryType.index_type)
}
inline ::schema_proto::DataType* DictionaryType::release_index_type() {
  
  ::schema_proto::DataType* temp = _impl_.index_type_;
  _impl_.index_type_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::schema_proto::DataType* DictionaryType::unsafe_arena_release_index_type() {
  // @@protoc_insertion_point(field_release:schema_proto.DictionaryType.index_type)
  
  ::schema_proto::DataType* temp = _impl_.index_type_;
  _impl_.index_type_ = nullptr;
  return temp;
}
inline ::schema_proto::DataType* DictionaryType::_internal_mutable_index_type() {
  
  if (_impl_.index_type_ == nullptr) {
    auto* p = CreateMaybeMessage<::schema_proto::DataType>(GetArenaForAllocation());
    _impl_.index_type_ = p;
  }
  return _impl_.index_type_;
}
inline ::schema_proto::DataType* DictionaryType::mutable_index_type() {
  ::schema_proto::DataType* _msg = _internal_mutable_index_type();
  // @@protoc_insertion_point(field_mutable:schema_proto.DictionaryType.index_type)
  return _msg;
}
inline void DictionaryType::set_allocated_index_type(::schema_proto::DataType* index_type) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.index_type_;
  }
  if (index_type) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(index_type);
    if (message_arena != submessage_arena) {
      index_type = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, index_type, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.index_type_ = index_type;
  // @@protoc_insertion_point(field_set_allocated:schema_proto.DictionaryType.index_type)
}

// .schema_proto.DataType value_type = 2;
inline bool DictionaryType::_internal_has_value_type() const {
  return this != internal_default_instance() && _impl_.value_type_ != nullptr;
}
inline bool DictionaryType::has_value_type() const {
  return _internal_has_value_type();
}
inline void DictionaryType::clear_value_type() {
  if (GetArenaForAllocation() == nullptr && _impl_.value_type_ != nullptr) {
    delete _impl_.value_type_;
  }
  _impl_.value_type_ = nullptr;
}
inline const ::schema_proto::DataType& DictionaryType::_internal_value_type() const {
  const ::schema_proto::DataType* p = _impl_.value_type_;
  return p != nullptr ? *p : reinterpret_cast<const ::schema_proto::DataType&>(
      ::schema_proto::_DataType_default_instance_);
}
inline const ::schema_proto::DataType& DictionaryType::value_type() const {
  // @@protoc_insertion_point(field_get:schema_proto.DictionaryType.value_type)
  return _internal_value_type();
}
inline void DictionaryType::unsafe_arena_set_allocated_value_type(
    ::schema_proto::DataType* value_type) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.value_type_);
  }
  _impl_.value_type_ = value_type;
  if (value_type) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:schema_proto.DictionaryType.value_type)
}
inline ::schema_proto::DataType* DictionaryType::release_value_type() {
  
  ::schema_proto::DataType* temp = _impl_.value_type_;
  _impl_.value_type_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::schema_proto::DataType* DictionaryType::unsafe_arena_release_value_type() {
  // @@protoc_insertion_point(field_release:schema_proto.DictionaryType.value_type)
  
  ::schema_proto::DataType* temp = _impl_.value_type_;
  _impl_.value_type_ = nullptr;
  return temp;
}
inline ::schema_proto::DataType* DictionaryType::_internal_mutable_value_type() {
  
  if (_impl_.value_type_ == nullptr) {
    auto* p = CreateMaybeMessage<::schema_proto::DataType>(GetArenaForAllocation());
    _impl_.value_type_ = p;
  }
  return _impl_.value_type_;
}
inline ::schema_proto::DataType* DictionaryType::mutable_value_type() {
  ::schema_proto::DataType* _msg = _internal_mutable_value_type();
  // @@protoc_insertion_point(field_mutable:schema_proto.DictionaryType.value_type)
  return _msg;
}
inline void DictionaryType::set_allocated_value_type(::schema_proto::DataType* value_type) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.value_type_;
  }
  if (value_type) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(value_type);
    if (message_arena != submessage_arena) {
      value_type = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, value_type, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.value_type_ = value_type;
  // @@protoc_insertion_point(field_set_allocated:schema_proto.DictionaryType.value_type)
}

// bool ordered = 3;
inline void DictionaryType::clear_ordered() {
  _impl_.ordered_ = false;
}
inline bool DictionaryType::_internal_ordered() const {
  return _impl_.ordered_;
}
inline bool DictionaryType::ordered() const {
  // @@protoc_insertion_point(field_get:schema_proto.DictionaryType.ordered)
  return _internal_ordered();
}
inline void DictionaryType::_internal_set_ordered(bool value) {
  
  _impl_.ordered_ = value;
}
inline void DictionaryType::set_ordered(bool value) {
  _internal_set_ordered(value);
  // @@protoc_insertion_point(field_set:schema_proto.DictionaryType.ordered)
}

// -------------------------------------------------------------------

// MapType

// bool keys_sorted = 1;
inline void MapType::clear_keys_sorted() {
  _impl_.keys_sorted_ = false;
}
inline bool MapType::_internal_keys_sorted() const {
  return _impl_.keys_sorted_;
}
inline bool MapType::keys_sorted() const {
  // @@protoc_insertion_point(field_get:schema_proto.MapType.keys_sorted)
  return _internal_keys_sorted();
}
inline void MapType::_internal_set_keys_sorted(bool value) {
  
  _impl_.keys_sorted_ = value;
}
inline void MapType::set_keys_sorted(bool value) {
  _internal_set_keys_sorted(value);
  // @@protoc_insertion_point(field_set:schema_proto.MapType.keys_sorted)
}

// -------------------------------------------------------------------

// DataType

// .schema_proto.FixedSizeBinaryType fixed_size_binary_type = 1;
inline bool DataType::_internal_has_fixed_size_binary_type() const {
  return type_related_values_case() == kFixedSizeBinaryType;
}
inline bool DataType::has_fixed_size_binary_type() const {
  return _internal_has_fixed_size_binary_type();
}
inline void DataType::set_has_fixed_size_binary_type() {
  _impl_._oneof_case_[0] = kFixedSizeBinaryType;
}
inline void DataType::clear_fixed_size_binary_type() {
  if (_internal_has_fixed_size_binary_type()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.type_related_values_.fixed_size_binary_type_;
    }
    clear_has_type_related_values();
  }
}
inline ::schema_proto::FixedSizeBinaryType* DataType::release_fixed_size_binary_type() {
  // @@protoc_insertion_point(field_release:schema_proto.DataType.fixed_size_binary_type)
  if (_internal_has_fixed_size_binary_type()) {
    clear_has_type_related_values();
    ::schema_proto::FixedSizeBinaryType* temp = _impl_.type_related_values_.fixed_size_binary_type_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.type_related_values_.fixed_size_binary_type_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::schema_proto::FixedSizeBinaryType& DataType::_internal_fixed_size_binary_type() const {
  return _internal_has_fixed_size_binary_type()
      ? *_impl_.type_related_values_.fixed_size_binary_type_
      : reinterpret_cast< ::schema_proto::FixedSizeBinaryType&>(::schema_proto::_FixedSizeBinaryType_default_instance_);
}
inline const ::schema_proto::FixedSizeBinaryType& DataType::fixed_size_binary_type() const {
  // @@protoc_insertion_point(field_get:schema_proto.DataType.fixed_size_binary_type)
  return _internal_fixed_size_binary_type();
}
inline ::schema_proto::FixedSizeBinaryType* DataType::unsafe_arena_release_fixed_size_binary_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:schema_proto.DataType.fixed_size_binary_type)
  if (_internal_has_fixed_size_binary_type()) {
    clear_has_type_related_values();
    ::schema_proto::FixedSizeBinaryType* temp = _impl_.type_related_values_.fixed_size_binary_type_;
    _impl_.type_related_values_.fixed_size_binary_type_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void DataType::unsafe_arena_set_allocated_fixed_size_binary_type(::schema_proto::FixedSizeBinaryType* fixed_size_binary_type) {
  clear_type_related_values();
  if (fixed_size_binary_type) {
    set_has_fixed_size_binary_type();
    _impl_.type_related_values_.fixed_size_binary_type_ = fixed_size_binary_type;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:schema_proto.DataType.fixed_size_binary_type)
}
inline ::schema_proto::FixedSizeBinaryType* DataType::_internal_mutable_fixed_size_binary_type() {
  if (!_internal_has_fixed_size_binary_type()) {
    clear_type_related_values();
    set_has_fixed_size_binary_type();
    _impl_.type_related_values_.fixed_size_binary_type_ = CreateMaybeMessage< ::schema_proto::FixedSizeBinaryType >(GetArenaForAllocation());
  }
  return _impl_.type_related_values_.fixed_size_binary_type_;
}
inline ::schema_proto::FixedSizeBinaryType* DataType::mutable_fixed_size_binary_type() {
  ::schema_proto::FixedSizeBinaryType* _msg = _internal_mutable_fixed_size_binary_type();
  // @@protoc_insertion_point(field_mutable:schema_proto.DataType.fixed_size_binary_type)
  return _msg;
}

// .schema_proto.FixedSizeListType fixed_size_list_type = 2;
inline bool DataType::_internal_has_fixed_size_list_type() const {
  return type_related_values_case() == kFixedSizeListType;
}
inline bool DataType::has_fixed_size_list_type() const {
  return _internal_has_fixed_size_list_type();
}
inline void DataType::set_has_fixed_size_list_type() {
  _impl_._oneof_case_[0] = kFixedSizeListType;
}
inline void DataType::clear_fixed_size_list_type() {
  if (_internal_has_fixed_size_list_type()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.type_related_values_.fixed_size_list_type_;
    }
    clear_has_type_related_values();
  }
}
inline ::schema_proto::FixedSizeListType* DataType::release_fixed_size_list_type() {
  // @@protoc_insertion_point(field_release:schema_proto.DataType.fixed_size_list_type)
  if (_internal_has_fixed_size_list_type()) {
    clear_has_type_related_values();
    ::schema_proto::FixedSizeListType* temp = _impl_.type_related_values_.fixed_size_list_type_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.type_related_values_.fixed_size_list_type_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::schema_proto::FixedSizeListType& DataType::_internal_fixed_size_list_type() const {
  return _internal_has_fixed_size_list_type()
      ? *_impl_.type_related_values_.fixed_size_list_type_
      : reinterpret_cast< ::schema_proto::FixedSizeListType&>(::schema_proto::_FixedSizeListType_default_instance_);
}
inline const ::schema_proto::FixedSizeListType& DataType::fixed_size_list_type() const {
  // @@protoc_insertion_point(field_get:schema_proto.DataType.fixed_size_list_type)
  return _internal_fixed_size_list_type();
}
inline ::schema_proto::FixedSizeListType* DataType::unsafe_arena_release_fixed_size_list_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:schema_proto.DataType.fixed_size_list_type)
  if (_internal_has_fixed_size_list_type()) {
    clear_has_type_related_values();
    ::schema_proto::FixedSizeListType* temp = _impl_.type_related_values_.fixed_size_list_type_;
    _impl_.type_related_values_.fixed_size_list_type_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void DataType::unsafe_arena_set_allocated_fixed_size_list_type(::schema_proto::FixedSizeListType* fixed_size_list_type) {
  clear_type_related_values();
  if (fixed_size_list_type) {
    set_has_fixed_size_list_type();
    _impl_.type_related_values_.fixed_size_list_type_ = fixed_size_list_type;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:schema_proto.DataType.fixed_size_list_type)
}
inline ::schema_proto::FixedSizeListType* DataType::_internal_mutable_fixed_size_list_type() {
  if (!_internal_has_fixed_size_list_type()) {
    clear_type_related_values();
    set_has_fixed_size_list_type();
    _impl_.type_related_values_.fixed_size_list_type_ = CreateMaybeMessage< ::schema_proto::FixedSizeListType >(GetArenaForAllocation());
  }
  return _impl_.type_related_values_.fixed_size_list_type_;
}
inline ::schema_proto::FixedSizeListType* DataType::mutable_fixed_size_list_type() {
  ::schema_proto::FixedSizeListType* _msg = _internal_mutable_fixed_size_list_type();
  // @@protoc_insertion_point(field_mutable:schema_proto.DataType.fixed_size_list_type)
  return _msg;
}

// .schema_proto.DictionaryType dictionary_type = 3;
inline bool DataType::_internal_has_dictionary_type() const {
  return type_related_values_case() == kDictionaryType;
}
inline bool DataType::has_dictionary_type() const {
  return _internal_has_dictionary_type();
}
inline void DataType::set_has_dictionary_type() {
  _impl_._oneof_case_[0] = kDictionaryType;
}
inline void DataType::clear_dictionary_type() {
  if (_internal_has_dictionary_type()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.type_related_values_.dictionary_type_;
    }
    clear_has_type_related_values();
  }
}
inline ::schema_proto::DictionaryType* DataType::release_dictionary_type() {
  // @@protoc_insertion_point(field_release:schema_proto.DataType.dictionary_type)
  if (_internal_has_dictionary_type()) {
    clear_has_type_related_values();
    ::schema_proto::DictionaryType* temp = _impl_.type_related_values_.dictionary_type_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.type_related_values_.dictionary_type_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::schema_proto::DictionaryType& DataType::_internal_dictionary_type() const {
  return _internal_has_dictionary_type()
      ? *_impl_.type_related_values_.dictionary_type_
      : reinterpret_cast< ::schema_proto::DictionaryType&>(::schema_proto::_DictionaryType_default_instance_);
}
inline const ::schema_proto::DictionaryType& DataType::dictionary_type() const {
  // @@protoc_insertion_point(field_get:schema_proto.DataType.dictionary_type)
  return _internal_dictionary_type();
}
inline ::schema_proto::DictionaryType* DataType::unsafe_arena_release_dictionary_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:schema_proto.DataType.dictionary_type)
  if (_internal_has_dictionary_type()) {
    clear_has_type_related_values();
    ::schema_proto::DictionaryType* temp = _impl_.type_related_values_.dictionary_type_;
    _impl_.type_related_values_.dictionary_type_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void DataType::unsafe_arena_set_allocated_dictionary_type(::schema_proto::DictionaryType* dictionary_type) {
  clear_type_related_values();
  if (dictionary_type) {
    set_has_dictionary_type();
    _impl_.type_related_values_.dictionary_type_ = dictionary_type;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:schema_proto.DataType.dictionary_type)
}
inline ::schema_proto::DictionaryType* DataType::_internal_mutable_dictionary_type() {
  if (!_internal_has_dictionary_type()) {
    clear_type_related_values();
    set_has_dictionary_type();
    _impl_.type_related_values_.dictionary_type_ = CreateMaybeMessage< ::schema_proto::DictionaryType >(GetArenaForAllocation());
  }
  return _impl_.type_related_values_.dictionary_type_;
}
inline ::schema_proto::DictionaryType* DataType::mutable_dictionary_type() {
  ::schema_proto::DictionaryType* _msg = _internal_mutable_dictionary_type();
  // @@protoc_insertion_point(field_mutable:schema_proto.DataType.dictionary_type)
  return _msg;
}

// .schema_proto.MapType map_type = 4;
inline bool DataType::_internal_has_map_type() const {
  return type_related_values_case() == kMapType;
}
inline bool DataType::has_map_type() const {
  return _internal_has_map_type();
}
inline void DataType::set_has_map_type() {
  _impl_._oneof_case_[0] = kMapType;
}
inline void DataType::clear_map_type() {
  if (_internal_has_map_type()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.type_related_values_.map_type_;
    }
    clear_has_type_related_values();
  }
}
inline ::schema_proto::MapType* DataType::release_map_type() {
  // @@protoc_insertion_point(field_release:schema_proto.DataType.map_type)
  if (_internal_has_map_type()) {
    clear_has_type_related_values();
    ::schema_proto::MapType* temp = _impl_.type_related_values_.map_type_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.type_related_values_.map_type_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::schema_proto::MapType& DataType::_internal_map_type() const {
  return _internal_has_map_type()
      ? *_impl_.type_related_values_.map_type_
      : reinterpret_cast< ::schema_proto::MapType&>(::schema_proto::_MapType_default_instance_);
}
inline const ::schema_proto::MapType& DataType::map_type() const {
  // @@protoc_insertion_point(field_get:schema_proto.DataType.map_type)
  return _internal_map_type();
}
inline ::schema_proto::MapType* DataType::unsafe_arena_release_map_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:schema_proto.DataType.map_type)
  if (_internal_has_map_type()) {
    clear_has_type_related_values();
    ::schema_proto::MapType* temp = _impl_.type_related_values_.map_type_;
    _impl_.type_related_values_.map_type_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void DataType::unsafe_arena_set_allocated_map_type(::schema_proto::MapType* map_type) {
  clear_type_related_values();
  if (map_type) {
    set_has_map_type();
    _impl_.type_related_values_.map_type_ = map_type;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:schema_proto.DataType.map_type)
}
inline ::schema_proto::MapType* DataType::_internal_mutable_map_type() {
  if (!_internal_has_map_type()) {
    clear_type_related_values();
    set_has_map_type();
    _impl_.type_related_values_.map_type_ = CreateMaybeMessage< ::schema_proto::MapType >(GetArenaForAllocation());
  }
  return _impl_.type_related_values_.map_type_;
}
inline ::schema_proto::MapType* DataType::mutable_map_type() {
  ::schema_proto::MapType* _msg = _internal_mutable_map_type();
  // @@protoc_insertion_point(field_mutable:schema_proto.DataType.map_type)
  return _msg;
}

// .schema_proto.LogicType logic_type = 100;
inline void DataType::clear_logic_type() {
  _impl_.logic_type_ = 0;
}
inline ::schema_proto::LogicType DataType::_internal_logic_type() const {
  return static_cast< ::schema_proto::LogicType >(_impl_.logic_type_);
}
inline ::schema_proto::LogicType DataType::logic_type() const {
  // @@protoc_insertion_point(field_get:schema_proto.DataType.logic_type)
  return _internal_logic_type();
}
inline void DataType::_internal_set_logic_type(::schema_proto::LogicType value) {
  
  _impl_.logic_type_ = value;
}
inline void DataType::set_logic_type(::schema_proto::LogicType value) {
  _internal_set_logic_type(value);
  // @@protoc_insertion_point(field_set:schema_proto.DataType.logic_type)
}

// repeated .schema_proto.Field children = 101;
inline int DataType::_internal_children_size() const {
  return _impl_.children_.size();
}
inline int DataType::children_size() const {
  return _internal_children_size();
}
inline void DataType::clear_children() {
  _impl_.children_.Clear();
}
inline ::schema_proto::Field* DataType::mutable_children(int index) {
  // @@protoc_insertion_point(field_mutable:schema_proto.DataType.children)
  return _impl_.children_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::schema_proto::Field >*
DataType::mutable_children() {
  // @@protoc_insertion_point(field_mutable_list:schema_proto.DataType.children)
  return &_impl_.children_;
}
inline const ::schema_proto::Field& DataType::_internal_children(int index) const {
  return _impl_.children_.Get(index);
}
inline const ::schema_proto::Field& DataType::children(int index) const {
  // @@protoc_insertion_point(field_get:schema_proto.DataType.children)
  return _internal_children(index);
}
inline ::schema_proto::Field* DataType::_internal_add_children() {
  return _impl_.children_.Add();
}
inline ::schema_proto::Field* DataType::add_children() {
  ::schema_proto::Field* _add = _internal_add_children();
  // @@protoc_insertion_point(field_add:schema_proto.DataType.children)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::schema_proto::Field >&
DataType::children() const {
  // @@protoc_insertion_point(field_list:schema_proto.DataType.children)
  return _impl_.children_;
}

inline bool DataType::has_type_related_values() const {
  return type_related_values_case() != TYPE_RELATED_VALUES_NOT_SET;
}
inline void DataType::clear_has_type_related_values() {
  _impl_._oneof_case_[0] = TYPE_RELATED_VALUES_NOT_SET;
}
inline DataType::TypeRelatedValuesCase DataType::type_related_values_case() const {
  return DataType::TypeRelatedValuesCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// KeyValueMetadata

// repeated string keys = 1;
inline int KeyValueMetadata::_internal_keys_size() const {
  return _impl_.keys_.size();
}
inline int KeyValueMetadata::keys_size() const {
  return _internal_keys_size();
}
inline void KeyValueMetadata::clear_keys() {
  _impl_.keys_.Clear();
}
inline std::string* KeyValueMetadata::add_keys() {
  std::string* _s = _internal_add_keys();
  // @@protoc_insertion_point(field_add_mutable:schema_proto.KeyValueMetadata.keys)
  return _s;
}
inline const std::string& KeyValueMetadata::_internal_keys(int index) const {
  return _impl_.keys_.Get(index);
}
inline const std::string& KeyValueMetadata::keys(int index) const {
  // @@protoc_insertion_point(field_get:schema_proto.KeyValueMetadata.keys)
  return _internal_keys(index);
}
inline std::string* KeyValueMetadata::mutable_keys(int index) {
  // @@protoc_insertion_point(field_mutable:schema_proto.KeyValueMetadata.keys)
  return _impl_.keys_.Mutable(index);
}
inline void KeyValueMetadata::set_keys(int index, const std::string& value) {
  _impl_.keys_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:schema_proto.KeyValueMetadata.keys)
}
inline void KeyValueMetadata::set_keys(int index, std::string&& value) {
  _impl_.keys_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:schema_proto.KeyValueMetadata.keys)
}
inline void KeyValueMetadata::set_keys(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.keys_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:schema_proto.KeyValueMetadata.keys)
}
inline void KeyValueMetadata::set_keys(int index, const char* value, size_t size) {
  _impl_.keys_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:schema_proto.KeyValueMetadata.keys)
}
inline std::string* KeyValueMetadata::_internal_add_keys() {
  return _impl_.keys_.Add();
}
inline void KeyValueMetadata::add_keys(const std::string& value) {
  _impl_.keys_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:schema_proto.KeyValueMetadata.keys)
}
inline void KeyValueMetadata::add_keys(std::string&& value) {
  _impl_.keys_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:schema_proto.KeyValueMetadata.keys)
}
inline void KeyValueMetadata::add_keys(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.keys_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:schema_proto.KeyValueMetadata.keys)
}
inline void KeyValueMetadata::add_keys(const char* value, size_t size) {
  _impl_.keys_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:schema_proto.KeyValueMetadata.keys)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
KeyValueMetadata::keys() const {
  // @@protoc_insertion_point(field_list:schema_proto.KeyValueMetadata.keys)
  return _impl_.keys_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
KeyValueMetadata::mutable_keys() {
  // @@protoc_insertion_point(field_mutable_list:schema_proto.KeyValueMetadata.keys)
  return &_impl_.keys_;
}

// repeated string values = 2;
inline int KeyValueMetadata::_internal_values_size() const {
  return _impl_.values_.size();
}
inline int KeyValueMetadata::values_size() const {
  return _internal_values_size();
}
inline void KeyValueMetadata::clear_values() {
  _impl_.values_.Clear();
}
inline std::string* KeyValueMetadata::add_values() {
  std::string* _s = _internal_add_values();
  // @@protoc_insertion_point(field_add_mutable:schema_proto.KeyValueMetadata.values)
  return _s;
}
inline const std::string& KeyValueMetadata::_internal_values(int index) const {
  return _impl_.values_.Get(index);
}
inline const std::string& KeyValueMetadata::values(int index) const {
  // @@protoc_insertion_point(field_get:schema_proto.KeyValueMetadata.values)
  return _internal_values(index);
}
inline std::string* KeyValueMetadata::mutable_values(int index) {
  // @@protoc_insertion_point(field_mutable:schema_proto.KeyValueMetadata.values)
  return _impl_.values_.Mutable(index);
}
inline void KeyValueMetadata::set_values(int index, const std::string& value) {
  _impl_.values_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:schema_proto.KeyValueMetadata.values)
}
inline void KeyValueMetadata::set_values(int index, std::string&& value) {
  _impl_.values_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:schema_proto.KeyValueMetadata.values)
}
inline void KeyValueMetadata::set_values(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.values_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:schema_proto.KeyValueMetadata.values)
}
inline void KeyValueMetadata::set_values(int index, const char* value, size_t size) {
  _impl_.values_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:schema_proto.KeyValueMetadata.values)
}
inline std::string* KeyValueMetadata::_internal_add_values() {
  return _impl_.values_.Add();
}
inline void KeyValueMetadata::add_values(const std::string& value) {
  _impl_.values_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:schema_proto.KeyValueMetadata.values)
}
inline void KeyValueMetadata::add_values(std::string&& value) {
  _impl_.values_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:schema_proto.KeyValueMetadata.values)
}
inline void KeyValueMetadata::add_values(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.values_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:schema_proto.KeyValueMetadata.values)
}
inline void KeyValueMetadata::add_values(const char* value, size_t size) {
  _impl_.values_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:schema_proto.KeyValueMetadata.values)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
KeyValueMetadata::values() const {
  // @@protoc_insertion_point(field_list:schema_proto.KeyValueMetadata.values)
  return _impl_.values_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
KeyValueMetadata::mutable_values() {
  // @@protoc_insertion_point(field_mutable_list:schema_proto.KeyValueMetadata.values)
  return &_impl_.values_;
}

// -------------------------------------------------------------------

// Field

// string name = 1;
inline void Field::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& Field::name() const {
  // @@protoc_insertion_point(field_get:schema_proto.Field.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Field::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:schema_proto.Field.name)
}
inline std::string* Field::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:schema_proto.Field.name)
  return _s;
}
inline const std::string& Field::_internal_name() const {
  return _impl_.name_.Get();
}
inline void Field::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* Field::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* Field::release_name() {
  // @@protoc_insertion_point(field_release:schema_proto.Field.name)
  return _impl_.name_.Release();
}
inline void Field::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:schema_proto.Field.name)
}

// bool nullable = 2;
inline void Field::clear_nullable() {
  _impl_.nullable_ = false;
}
inline bool Field::_internal_nullable() const {
  return _impl_.nullable_;
}
inline bool Field::nullable() const {
  // @@protoc_insertion_point(field_get:schema_proto.Field.nullable)
  return _internal_nullable();
}
inline void Field::_internal_set_nullable(bool value) {
  
  _impl_.nullable_ = value;
}
inline void Field::set_nullable(bool value) {
  _internal_set_nullable(value);
  // @@protoc_insertion_point(field_set:schema_proto.Field.nullable)
}

// .schema_proto.DataType data_type = 3;
inline bool Field::_internal_has_data_type() const {
  return this != internal_default_instance() && _impl_.data_type_ != nullptr;
}
inline bool Field::has_data_type() const {
  return _internal_has_data_type();
}
inline void Field::clear_data_type() {
  if (GetArenaForAllocation() == nullptr && _impl_.data_type_ != nullptr) {
    delete _impl_.data_type_;
  }
  _impl_.data_type_ = nullptr;
}
inline const ::schema_proto::DataType& Field::_internal_data_type() const {
  const ::schema_proto::DataType* p = _impl_.data_type_;
  return p != nullptr ? *p : reinterpret_cast<const ::schema_proto::DataType&>(
      ::schema_proto::_DataType_default_instance_);
}
inline const ::schema_proto::DataType& Field::data_type() const {
  // @@protoc_insertion_point(field_get:schema_proto.Field.data_type)
  return _internal_data_type();
}
inline void Field::unsafe_arena_set_allocated_data_type(
    ::schema_proto::DataType* data_type) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.data_type_);
  }
  _impl_.data_type_ = data_type;
  if (data_type) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:schema_proto.Field.data_type)
}
inline ::schema_proto::DataType* Field::release_data_type() {
  
  ::schema_proto::DataType* temp = _impl_.data_type_;
  _impl_.data_type_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::schema_proto::DataType* Field::unsafe_arena_release_data_type() {
  // @@protoc_insertion_point(field_release:schema_proto.Field.data_type)
  
  ::schema_proto::DataType* temp = _impl_.data_type_;
  _impl_.data_type_ = nullptr;
  return temp;
}
inline ::schema_proto::DataType* Field::_internal_mutable_data_type() {
  
  if (_impl_.data_type_ == nullptr) {
    auto* p = CreateMaybeMessage<::schema_proto::DataType>(GetArenaForAllocation());
    _impl_.data_type_ = p;
  }
  return _impl_.data_type_;
}
inline ::schema_proto::DataType* Field::mutable_data_type() {
  ::schema_proto::DataType* _msg = _internal_mutable_data_type();
  // @@protoc_insertion_point(field_mutable:schema_proto.Field.data_type)
  return _msg;
}
inline void Field::set_allocated_data_type(::schema_proto::DataType* data_type) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.data_type_;
  }
  if (data_type) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(data_type);
    if (message_arena != submessage_arena) {
      data_type = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, data_type, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.data_type_ = data_type;
  // @@protoc_insertion_point(field_set_allocated:schema_proto.Field.data_type)
}

// .schema_proto.KeyValueMetadata metadata = 4;
inline bool Field::_internal_has_metadata() const {
  return this != internal_default_instance() && _impl_.metadata_ != nullptr;
}
inline bool Field::has_metadata() const {
  return _internal_has_metadata();
}
inline void Field::clear_metadata() {
  if (GetArenaForAllocation() == nullptr && _impl_.metadata_ != nullptr) {
    delete _impl_.metadata_;
  }
  _impl_.metadata_ = nullptr;
}
inline const ::schema_proto::KeyValueMetadata& Field::_internal_metadata() const {
  const ::schema_proto::KeyValueMetadata* p = _impl_.metadata_;
  return p != nullptr ? *p : reinterpret_cast<const ::schema_proto::KeyValueMetadata&>(
      ::schema_proto::_KeyValueMetadata_default_instance_);
}
inline const ::schema_proto::KeyValueMetadata& Field::metadata() const {
  // @@protoc_insertion_point(field_get:schema_proto.Field.metadata)
  return _internal_metadata();
}
inline void Field::unsafe_arena_set_allocated_metadata(
    ::schema_proto::KeyValueMetadata* metadata) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.metadata_);
  }
  _impl_.metadata_ = metadata;
  if (metadata) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:schema_proto.Field.metadata)
}
inline ::schema_proto::KeyValueMetadata* Field::release_metadata() {
  
  ::schema_proto::KeyValueMetadata* temp = _impl_.metadata_;
  _impl_.metadata_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::schema_proto::KeyValueMetadata* Field::unsafe_arena_release_metadata() {
  // @@protoc_insertion_point(field_release:schema_proto.Field.metadata)
  
  ::schema_proto::KeyValueMetadata* temp = _impl_.metadata_;
  _impl_.metadata_ = nullptr;
  return temp;
}
inline ::schema_proto::KeyValueMetadata* Field::_internal_mutable_metadata() {
  
  if (_impl_.metadata_ == nullptr) {
    auto* p = CreateMaybeMessage<::schema_proto::KeyValueMetadata>(GetArenaForAllocation());
    _impl_.metadata_ = p;
  }
  return _impl_.metadata_;
}
inline ::schema_proto::KeyValueMetadata* Field::mutable_metadata() {
  ::schema_proto::KeyValueMetadata* _msg = _internal_mutable_metadata();
  // @@protoc_insertion_point(field_mutable:schema_proto.Field.metadata)
  return _msg;
}
inline void Field::set_allocated_metadata(::schema_proto::KeyValueMetadata* metadata) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.metadata_;
  }
  if (metadata) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(metadata);
    if (message_arena != submessage_arena) {
      metadata = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, metadata, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.metadata_ = metadata;
  // @@protoc_insertion_point(field_set_allocated:schema_proto.Field.metadata)
}

// -------------------------------------------------------------------

// SchemaOptions

// string primary_column = 1;
inline void SchemaOptions::clear_primary_column() {
  _impl_.primary_column_.ClearToEmpty();
}
inline const std::string& SchemaOptions::primary_column() const {
  // @@protoc_insertion_point(field_get:schema_proto.SchemaOptions.primary_column)
  return _internal_primary_column();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SchemaOptions::set_primary_column(ArgT0&& arg0, ArgT... args) {
 
 _impl_.primary_column_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:schema_proto.SchemaOptions.primary_column)
}
inline std::string* SchemaOptions::mutable_primary_column() {
  std::string* _s = _internal_mutable_primary_column();
  // @@protoc_insertion_point(field_mutable:schema_proto.SchemaOptions.primary_column)
  return _s;
}
inline const std::string& SchemaOptions::_internal_primary_column() const {
  return _impl_.primary_column_.Get();
}
inline void SchemaOptions::_internal_set_primary_column(const std::string& value) {
  
  _impl_.primary_column_.Set(value, GetArenaForAllocation());
}
inline std::string* SchemaOptions::_internal_mutable_primary_column() {
  
  return _impl_.primary_column_.Mutable(GetArenaForAllocation());
}
inline std::string* SchemaOptions::release_primary_column() {
  // @@protoc_insertion_point(field_release:schema_proto.SchemaOptions.primary_column)
  return _impl_.primary_column_.Release();
}
inline void SchemaOptions::set_allocated_primary_column(std::string* primary_column) {
  if (primary_column != nullptr) {
    
  } else {
    
  }
  _impl_.primary_column_.SetAllocated(primary_column, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.primary_column_.IsDefault()) {
    _impl_.primary_column_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:schema_proto.SchemaOptions.primary_column)
}

// string version_column = 2;
inline void SchemaOptions::clear_version_column() {
  _impl_.version_column_.ClearToEmpty();
}
inline const std::string& SchemaOptions::version_column() const {
  // @@protoc_insertion_point(field_get:schema_proto.SchemaOptions.version_column)
  return _internal_version_column();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SchemaOptions::set_version_column(ArgT0&& arg0, ArgT... args) {
 
 _impl_.version_column_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:schema_proto.SchemaOptions.version_column)
}
inline std::string* SchemaOptions::mutable_version_column() {
  std::string* _s = _internal_mutable_version_column();
  // @@protoc_insertion_point(field_mutable:schema_proto.SchemaOptions.version_column)
  return _s;
}
inline const std::string& SchemaOptions::_internal_version_column() const {
  return _impl_.version_column_.Get();
}
inline void SchemaOptions::_internal_set_version_column(const std::string& value) {
  
  _impl_.version_column_.Set(value, GetArenaForAllocation());
}
inline std::string* SchemaOptions::_internal_mutable_version_column() {
  
  return _impl_.version_column_.Mutable(GetArenaForAllocation());
}
inline std::string* SchemaOptions::release_version_column() {
  // @@protoc_insertion_point(field_release:schema_proto.SchemaOptions.version_column)
  return _impl_.version_column_.Release();
}
inline void SchemaOptions::set_allocated_version_column(std::string* version_column) {
  if (version_column != nullptr) {
    
  } else {
    
  }
  _impl_.version_column_.SetAllocated(version_column, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.version_column_.IsDefault()) {
    _impl_.version_column_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:schema_proto.SchemaOptions.version_column)
}

// string vector_column = 3;
inline void SchemaOptions::clear_vector_column() {
  _impl_.vector_column_.ClearToEmpty();
}
inline const std::string& SchemaOptions::vector_column() const {
  // @@protoc_insertion_point(field_get:schema_proto.SchemaOptions.vector_column)
  return _internal_vector_column();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SchemaOptions::set_vector_column(ArgT0&& arg0, ArgT... args) {
 
 _impl_.vector_column_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:schema_proto.SchemaOptions.vector_column)
}
inline std::string* SchemaOptions::mutable_vector_column() {
  std::string* _s = _internal_mutable_vector_column();
  // @@protoc_insertion_point(field_mutable:schema_proto.SchemaOptions.vector_column)
  return _s;
}
inline const std::string& SchemaOptions::_internal_vector_column() const {
  return _impl_.vector_column_.Get();
}
inline void SchemaOptions::_internal_set_vector_column(const std::string& value) {
  
  _impl_.vector_column_.Set(value, GetArenaForAllocation());
}
inline std::string* SchemaOptions::_internal_mutable_vector_column() {
  
  return _impl_.vector_column_.Mutable(GetArenaForAllocation());
}
inline std::string* SchemaOptions::release_vector_column() {
  // @@protoc_insertion_point(field_release:schema_proto.SchemaOptions.vector_column)
  return _impl_.vector_column_.Release();
}
inline void SchemaOptions::set_allocated_vector_column(std::string* vector_column) {
  if (vector_column != nullptr) {
    
  } else {
    
  }
  _impl_.vector_column_.SetAllocated(vector_column, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.vector_column_.IsDefault()) {
    _impl_.vector_column_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:schema_proto.SchemaOptions.vector_column)
}

// -------------------------------------------------------------------

// ArrowSchema

// repeated .schema_proto.Field fields = 1;
inline int ArrowSchema::_internal_fields_size() const {
  return _impl_.fields_.size();
}
inline int ArrowSchema::fields_size() const {
  return _internal_fields_size();
}
inline void ArrowSchema::clear_fields() {
  _impl_.fields_.Clear();
}
inline ::schema_proto::Field* ArrowSchema::mutable_fields(int index) {
  // @@protoc_insertion_point(field_mutable:schema_proto.ArrowSchema.fields)
  return _impl_.fields_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::schema_proto::Field >*
ArrowSchema::mutable_fields() {
  // @@protoc_insertion_point(field_mutable_list:schema_proto.ArrowSchema.fields)
  return &_impl_.fields_;
}
inline const ::schema_proto::Field& ArrowSchema::_internal_fields(int index) const {
  return _impl_.fields_.Get(index);
}
inline const ::schema_proto::Field& ArrowSchema::fields(int index) const {
  // @@protoc_insertion_point(field_get:schema_proto.ArrowSchema.fields)
  return _internal_fields(index);
}
inline ::schema_proto::Field* ArrowSchema::_internal_add_fields() {
  return _impl_.fields_.Add();
}
inline ::schema_proto::Field* ArrowSchema::add_fields() {
  ::schema_proto::Field* _add = _internal_add_fields();
  // @@protoc_insertion_point(field_add:schema_proto.ArrowSchema.fields)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::schema_proto::Field >&
ArrowSchema::fields() const {
  // @@protoc_insertion_point(field_list:schema_proto.ArrowSchema.fields)
  return _impl_.fields_;
}

// .schema_proto.Endianness endianness = 2;
inline void ArrowSchema::clear_endianness() {
  _impl_.endianness_ = 0;
}
inline ::schema_proto::Endianness ArrowSchema::_internal_endianness() const {
  return static_cast< ::schema_proto::Endianness >(_impl_.endianness_);
}
inline ::schema_proto::Endianness ArrowSchema::endianness() const {
  // @@protoc_insertion_point(field_get:schema_proto.ArrowSchema.endianness)
  return _internal_endianness();
}
inline void ArrowSchema::_internal_set_endianness(::schema_proto::Endianness value) {
  
  _impl_.endianness_ = value;
}
inline void ArrowSchema::set_endianness(::schema_proto::Endianness value) {
  _internal_set_endianness(value);
  // @@protoc_insertion_point(field_set:schema_proto.ArrowSchema.endianness)
}

// .schema_proto.KeyValueMetadata metadata = 3;
inline bool ArrowSchema::_internal_has_metadata() const {
  return this != internal_default_instance() && _impl_.metadata_ != nullptr;
}
inline bool ArrowSchema::has_metadata() const {
  return _internal_has_metadata();
}
inline void ArrowSchema::clear_metadata() {
  if (GetArenaForAllocation() == nullptr && _impl_.metadata_ != nullptr) {
    delete _impl_.metadata_;
  }
  _impl_.metadata_ = nullptr;
}
inline const ::schema_proto::KeyValueMetadata& ArrowSchema::_internal_metadata() const {
  const ::schema_proto::KeyValueMetadata* p = _impl_.metadata_;
  return p != nullptr ? *p : reinterpret_cast<const ::schema_proto::KeyValueMetadata&>(
      ::schema_proto::_KeyValueMetadata_default_instance_);
}
inline const ::schema_proto::KeyValueMetadata& ArrowSchema::metadata() const {
  // @@protoc_insertion_point(field_get:schema_proto.ArrowSchema.metadata)
  return _internal_metadata();
}
inline void ArrowSchema::unsafe_arena_set_allocated_metadata(
    ::schema_proto::KeyValueMetadata* metadata) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.metadata_);
  }
  _impl_.metadata_ = metadata;
  if (metadata) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:schema_proto.ArrowSchema.metadata)
}
inline ::schema_proto::KeyValueMetadata* ArrowSchema::release_metadata() {
  
  ::schema_proto::KeyValueMetadata* temp = _impl_.metadata_;
  _impl_.metadata_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::schema_proto::KeyValueMetadata* ArrowSchema::unsafe_arena_release_metadata() {
  // @@protoc_insertion_point(field_release:schema_proto.ArrowSchema.metadata)
  
  ::schema_proto::KeyValueMetadata* temp = _impl_.metadata_;
  _impl_.metadata_ = nullptr;
  return temp;
}
inline ::schema_proto::KeyValueMetadata* ArrowSchema::_internal_mutable_metadata() {
  
  if (_impl_.metadata_ == nullptr) {
    auto* p = CreateMaybeMessage<::schema_proto::KeyValueMetadata>(GetArenaForAllocation());
    _impl_.metadata_ = p;
  }
  return _impl_.metadata_;
}
inline ::schema_proto::KeyValueMetadata* ArrowSchema::mutable_metadata() {
  ::schema_proto::KeyValueMetadata* _msg = _internal_mutable_metadata();
  // @@protoc_insertion_point(field_mutable:schema_proto.ArrowSchema.metadata)
  return _msg;
}
inline void ArrowSchema::set_allocated_metadata(::schema_proto::KeyValueMetadata* metadata) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.metadata_;
  }
  if (metadata) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(metadata);
    if (message_arena != submessage_arena) {
      metadata = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, metadata, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.metadata_ = metadata;
  // @@protoc_insertion_point(field_set_allocated:schema_proto.ArrowSchema.metadata)
}

// -------------------------------------------------------------------

// Schema

// .schema_proto.ArrowSchema arrow_schema = 1;
inline bool Schema::_internal_has_arrow_schema() const {
  return this != internal_default_instance() && _impl_.arrow_schema_ != nullptr;
}
inline bool Schema::has_arrow_schema() const {
  return _internal_has_arrow_schema();
}
inline void Schema::clear_arrow_schema() {
  if (GetArenaForAllocation() == nullptr && _impl_.arrow_schema_ != nullptr) {
    delete _impl_.arrow_schema_;
  }
  _impl_.arrow_schema_ = nullptr;
}
inline const ::schema_proto::ArrowSchema& Schema::_internal_arrow_schema() const {
  const ::schema_proto::ArrowSchema* p = _impl_.arrow_schema_;
  return p != nullptr ? *p : reinterpret_cast<const ::schema_proto::ArrowSchema&>(
      ::schema_proto::_ArrowSchema_default_instance_);
}
inline const ::schema_proto::ArrowSchema& Schema::arrow_schema() const {
  // @@protoc_insertion_point(field_get:schema_proto.Schema.arrow_schema)
  return _internal_arrow_schema();
}
inline void Schema::unsafe_arena_set_allocated_arrow_schema(
    ::schema_proto::ArrowSchema* arrow_schema) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.arrow_schema_);
  }
  _impl_.arrow_schema_ = arrow_schema;
  if (arrow_schema) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:schema_proto.Schema.arrow_schema)
}
inline ::schema_proto::ArrowSchema* Schema::release_arrow_schema() {
  
  ::schema_proto::ArrowSchema* temp = _impl_.arrow_schema_;
  _impl_.arrow_schema_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::schema_proto::ArrowSchema* Schema::unsafe_arena_release_arrow_schema() {
  // @@protoc_insertion_point(field_release:schema_proto.Schema.arrow_schema)
  
  ::schema_proto::ArrowSchema* temp = _impl_.arrow_schema_;
  _impl_.arrow_schema_ = nullptr;
  return temp;
}
inline ::schema_proto::ArrowSchema* Schema::_internal_mutable_arrow_schema() {
  
  if (_impl_.arrow_schema_ == nullptr) {
    auto* p = CreateMaybeMessage<::schema_proto::ArrowSchema>(GetArenaForAllocation());
    _impl_.arrow_schema_ = p;
  }
  return _impl_.arrow_schema_;
}
inline ::schema_proto::ArrowSchema* Schema::mutable_arrow_schema() {
  ::schema_proto::ArrowSchema* _msg = _internal_mutable_arrow_schema();
  // @@protoc_insertion_point(field_mutable:schema_proto.Schema.arrow_schema)
  return _msg;
}
inline void Schema::set_allocated_arrow_schema(::schema_proto::ArrowSchema* arrow_schema) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.arrow_schema_;
  }
  if (arrow_schema) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(arrow_schema);
    if (message_arena != submessage_arena) {
      arrow_schema = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, arrow_schema, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.arrow_schema_ = arrow_schema;
  // @@protoc_insertion_point(field_set_allocated:schema_proto.Schema.arrow_schema)
}

// .schema_proto.SchemaOptions schema_options = 2;
inline bool Schema::_internal_has_schema_options() const {
  return this != internal_default_instance() && _impl_.schema_options_ != nullptr;
}
inline bool Schema::has_schema_options() const {
  return _internal_has_schema_options();
}
inline void Schema::clear_schema_options() {
  if (GetArenaForAllocation() == nullptr && _impl_.schema_options_ != nullptr) {
    delete _impl_.schema_options_;
  }
  _impl_.schema_options_ = nullptr;
}
inline const ::schema_proto::SchemaOptions& Schema::_internal_schema_options() const {
  const ::schema_proto::SchemaOptions* p = _impl_.schema_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::schema_proto::SchemaOptions&>(
      ::schema_proto::_SchemaOptions_default_instance_);
}
inline const ::schema_proto::SchemaOptions& Schema::schema_options() const {
  // @@protoc_insertion_point(field_get:schema_proto.Schema.schema_options)
  return _internal_schema_options();
}
inline void Schema::unsafe_arena_set_allocated_schema_options(
    ::schema_proto::SchemaOptions* schema_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.schema_options_);
  }
  _impl_.schema_options_ = schema_options;
  if (schema_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:schema_proto.Schema.schema_options)
}
inline ::schema_proto::SchemaOptions* Schema::release_schema_options() {
  
  ::schema_proto::SchemaOptions* temp = _impl_.schema_options_;
  _impl_.schema_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::schema_proto::SchemaOptions* Schema::unsafe_arena_release_schema_options() {
  // @@protoc_insertion_point(field_release:schema_proto.Schema.schema_options)
  
  ::schema_proto::SchemaOptions* temp = _impl_.schema_options_;
  _impl_.schema_options_ = nullptr;
  return temp;
}
inline ::schema_proto::SchemaOptions* Schema::_internal_mutable_schema_options() {
  
  if (_impl_.schema_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::schema_proto::SchemaOptions>(GetArenaForAllocation());
    _impl_.schema_options_ = p;
  }
  return _impl_.schema_options_;
}
inline ::schema_proto::SchemaOptions* Schema::mutable_schema_options() {
  ::schema_proto::SchemaOptions* _msg = _internal_mutable_schema_options();
  // @@protoc_insertion_point(field_mutable:schema_proto.Schema.schema_options)
  return _msg;
}
inline void Schema::set_allocated_schema_options(::schema_proto::SchemaOptions* schema_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.schema_options_;
  }
  if (schema_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(schema_options);
    if (message_arena != submessage_arena) {
      schema_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, schema_options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.schema_options_ = schema_options;
  // @@protoc_insertion_point(field_set_allocated:schema_proto.Schema.schema_options)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace schema_proto

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::schema_proto::LogicType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::schema_proto::LogicType>() {
  return ::schema_proto::LogicType_descriptor();
}
template <> struct is_proto_enum< ::schema_proto::Endianness> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::schema_proto::Endianness>() {
  return ::schema_proto::Endianness_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_schema_5farrow_2eproto
