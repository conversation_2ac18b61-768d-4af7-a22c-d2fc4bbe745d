// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: schema_arrow.proto

#include "schema_arrow.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG

namespace _pb = ::PROTOBUF_NAMESPACE_ID;
namespace _pbi = _pb::internal;

namespace schema_proto {
PROTOBUF_CONSTEXPR FixedSizeBinaryType::FixedSizeBinaryType(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.byte_width_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct FixedSizeBinaryTypeDefaultTypeInternal {
  PROTOBUF_CONSTEXPR FixedSizeBinaryTypeDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~FixedSizeBinaryTypeDefaultTypeInternal() {}
  union {
    FixedSizeBinaryType _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 FixedSizeBinaryTypeDefaultTypeInternal _FixedSizeBinaryType_default_instance_;
PROTOBUF_CONSTEXPR FixedSizeListType::FixedSizeListType(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.list_size_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct FixedSizeListTypeDefaultTypeInternal {
  PROTOBUF_CONSTEXPR FixedSizeListTypeDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~FixedSizeListTypeDefaultTypeInternal() {}
  union {
    FixedSizeListType _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 FixedSizeListTypeDefaultTypeInternal _FixedSizeListType_default_instance_;
PROTOBUF_CONSTEXPR DictionaryType::DictionaryType(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.index_type_)*/nullptr
  , /*decltype(_impl_.value_type_)*/nullptr
  , /*decltype(_impl_.ordered_)*/false
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct DictionaryTypeDefaultTypeInternal {
  PROTOBUF_CONSTEXPR DictionaryTypeDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~DictionaryTypeDefaultTypeInternal() {}
  union {
    DictionaryType _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 DictionaryTypeDefaultTypeInternal _DictionaryType_default_instance_;
PROTOBUF_CONSTEXPR MapType::MapType(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.keys_sorted_)*/false
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct MapTypeDefaultTypeInternal {
  PROTOBUF_CONSTEXPR MapTypeDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~MapTypeDefaultTypeInternal() {}
  union {
    MapType _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 MapTypeDefaultTypeInternal _MapType_default_instance_;
PROTOBUF_CONSTEXPR DataType::DataType(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.children_)*/{}
  , /*decltype(_impl_.logic_type_)*/0
  , /*decltype(_impl_.type_related_values_)*/{}
  , /*decltype(_impl_._cached_size_)*/{}
  , /*decltype(_impl_._oneof_case_)*/{}} {}
struct DataTypeDefaultTypeInternal {
  PROTOBUF_CONSTEXPR DataTypeDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~DataTypeDefaultTypeInternal() {}
  union {
    DataType _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 DataTypeDefaultTypeInternal _DataType_default_instance_;
PROTOBUF_CONSTEXPR KeyValueMetadata::KeyValueMetadata(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.keys_)*/{}
  , /*decltype(_impl_.values_)*/{}
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct KeyValueMetadataDefaultTypeInternal {
  PROTOBUF_CONSTEXPR KeyValueMetadataDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~KeyValueMetadataDefaultTypeInternal() {}
  union {
    KeyValueMetadata _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 KeyValueMetadataDefaultTypeInternal _KeyValueMetadata_default_instance_;
PROTOBUF_CONSTEXPR Field::Field(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.name_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.data_type_)*/nullptr
  , /*decltype(_impl_.metadata_)*/nullptr
  , /*decltype(_impl_.nullable_)*/false
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct FieldDefaultTypeInternal {
  PROTOBUF_CONSTEXPR FieldDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~FieldDefaultTypeInternal() {}
  union {
    Field _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 FieldDefaultTypeInternal _Field_default_instance_;
PROTOBUF_CONSTEXPR SchemaOptions::SchemaOptions(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.primary_column_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.version_column_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.vector_column_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct SchemaOptionsDefaultTypeInternal {
  PROTOBUF_CONSTEXPR SchemaOptionsDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~SchemaOptionsDefaultTypeInternal() {}
  union {
    SchemaOptions _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 SchemaOptionsDefaultTypeInternal _SchemaOptions_default_instance_;
PROTOBUF_CONSTEXPR ArrowSchema::ArrowSchema(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.fields_)*/{}
  , /*decltype(_impl_.metadata_)*/nullptr
  , /*decltype(_impl_.endianness_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct ArrowSchemaDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ArrowSchemaDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~ArrowSchemaDefaultTypeInternal() {}
  union {
    ArrowSchema _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ArrowSchemaDefaultTypeInternal _ArrowSchema_default_instance_;
PROTOBUF_CONSTEXPR Schema::Schema(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.arrow_schema_)*/nullptr
  , /*decltype(_impl_.schema_options_)*/nullptr
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct SchemaDefaultTypeInternal {
  PROTOBUF_CONSTEXPR SchemaDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~SchemaDefaultTypeInternal() {}
  union {
    Schema _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 SchemaDefaultTypeInternal _Schema_default_instance_;
}  // namespace schema_proto
static ::_pb::Metadata file_level_metadata_schema_5farrow_2eproto[10];
static const ::_pb::EnumDescriptor* file_level_enum_descriptors_schema_5farrow_2eproto[2];
static constexpr ::_pb::ServiceDescriptor const** file_level_service_descriptors_schema_5farrow_2eproto = nullptr;

const uint32_t TableStruct_schema_5farrow_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::schema_proto::FixedSizeBinaryType, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::schema_proto::FixedSizeBinaryType, _impl_.byte_width_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::schema_proto::FixedSizeListType, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::schema_proto::FixedSizeListType, _impl_.list_size_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::schema_proto::DictionaryType, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::schema_proto::DictionaryType, _impl_.index_type_),
  PROTOBUF_FIELD_OFFSET(::schema_proto::DictionaryType, _impl_.value_type_),
  PROTOBUF_FIELD_OFFSET(::schema_proto::DictionaryType, _impl_.ordered_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::schema_proto::MapType, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::schema_proto::MapType, _impl_.keys_sorted_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::schema_proto::DataType, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::schema_proto::DataType, _impl_._oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ::_pbi::kInvalidFieldOffsetTag,
  ::_pbi::kInvalidFieldOffsetTag,
  ::_pbi::kInvalidFieldOffsetTag,
  ::_pbi::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::schema_proto::DataType, _impl_.logic_type_),
  PROTOBUF_FIELD_OFFSET(::schema_proto::DataType, _impl_.children_),
  PROTOBUF_FIELD_OFFSET(::schema_proto::DataType, _impl_.type_related_values_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::schema_proto::KeyValueMetadata, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::schema_proto::KeyValueMetadata, _impl_.keys_),
  PROTOBUF_FIELD_OFFSET(::schema_proto::KeyValueMetadata, _impl_.values_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::schema_proto::Field, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::schema_proto::Field, _impl_.name_),
  PROTOBUF_FIELD_OFFSET(::schema_proto::Field, _impl_.nullable_),
  PROTOBUF_FIELD_OFFSET(::schema_proto::Field, _impl_.data_type_),
  PROTOBUF_FIELD_OFFSET(::schema_proto::Field, _impl_.metadata_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::schema_proto::SchemaOptions, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::schema_proto::SchemaOptions, _impl_.primary_column_),
  PROTOBUF_FIELD_OFFSET(::schema_proto::SchemaOptions, _impl_.version_column_),
  PROTOBUF_FIELD_OFFSET(::schema_proto::SchemaOptions, _impl_.vector_column_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::schema_proto::ArrowSchema, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::schema_proto::ArrowSchema, _impl_.fields_),
  PROTOBUF_FIELD_OFFSET(::schema_proto::ArrowSchema, _impl_.endianness_),
  PROTOBUF_FIELD_OFFSET(::schema_proto::ArrowSchema, _impl_.metadata_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::schema_proto::Schema, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::schema_proto::Schema, _impl_.arrow_schema_),
  PROTOBUF_FIELD_OFFSET(::schema_proto::Schema, _impl_.schema_options_),
};
static const ::_pbi::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::schema_proto::FixedSizeBinaryType)},
  { 7, -1, -1, sizeof(::schema_proto::FixedSizeListType)},
  { 14, -1, -1, sizeof(::schema_proto::DictionaryType)},
  { 23, -1, -1, sizeof(::schema_proto::MapType)},
  { 30, -1, -1, sizeof(::schema_proto::DataType)},
  { 43, -1, -1, sizeof(::schema_proto::KeyValueMetadata)},
  { 51, -1, -1, sizeof(::schema_proto::Field)},
  { 61, -1, -1, sizeof(::schema_proto::SchemaOptions)},
  { 70, -1, -1, sizeof(::schema_proto::ArrowSchema)},
  { 79, -1, -1, sizeof(::schema_proto::Schema)},
};

static const ::_pb::Message* const file_default_instances[] = {
  &::schema_proto::_FixedSizeBinaryType_default_instance_._instance,
  &::schema_proto::_FixedSizeListType_default_instance_._instance,
  &::schema_proto::_DictionaryType_default_instance_._instance,
  &::schema_proto::_MapType_default_instance_._instance,
  &::schema_proto::_DataType_default_instance_._instance,
  &::schema_proto::_KeyValueMetadata_default_instance_._instance,
  &::schema_proto::_Field_default_instance_._instance,
  &::schema_proto::_SchemaOptions_default_instance_._instance,
  &::schema_proto::_ArrowSchema_default_instance_._instance,
  &::schema_proto::_Schema_default_instance_._instance,
};

const char descriptor_table_protodef_schema_5farrow_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\022schema_arrow.proto\022\014schema_proto\")\n\023Fi"
  "xedSizeBinaryType\022\022\n\nbyte_width\030\001 \001(\005\"&\n"
  "\021FixedSizeListType\022\021\n\tlist_size\030\001 \001(\005\"y\n"
  "\016DictionaryType\022*\n\nindex_type\030\001 \001(\0132\026.sc"
  "hema_proto.DataType\022*\n\nvalue_type\030\002 \001(\0132"
  "\026.schema_proto.DataType\022\017\n\007ordered\030\003 \001(\010"
  "\"\036\n\007MapType\022\023\n\013keys_sorted\030\001 \001(\010\"\337\002\n\010Dat"
  "aType\022C\n\026fixed_size_binary_type\030\001 \001(\0132!."
  "schema_proto.FixedSizeBinaryTypeH\000\022\?\n\024fi"
  "xed_size_list_type\030\002 \001(\0132\037.schema_proto."
  "FixedSizeListTypeH\000\0227\n\017dictionary_type\030\003"
  " \001(\0132\034.schema_proto.DictionaryTypeH\000\022)\n\010"
  "map_type\030\004 \001(\0132\025.schema_proto.MapTypeH\000\022"
  "+\n\nlogic_type\030d \001(\0162\027.schema_proto.Logic"
  "Type\022%\n\010children\030e \003(\0132\023.schema_proto.Fi"
  "eldB\025\n\023type_related_values\"0\n\020KeyValueMe"
  "tadata\022\014\n\004keys\030\001 \003(\t\022\016\n\006values\030\002 \003(\t\"\204\001\n"
  "\005Field\022\014\n\004name\030\001 \001(\t\022\020\n\010nullable\030\002 \001(\010\022)"
  "\n\tdata_type\030\003 \001(\0132\026.schema_proto.DataTyp"
  "e\0220\n\010metadata\030\004 \001(\0132\036.schema_proto.KeyVa"
  "lueMetadata\"V\n\rSchemaOptions\022\026\n\016primary_"
  "column\030\001 \001(\t\022\026\n\016version_column\030\002 \001(\t\022\025\n\r"
  "vector_column\030\003 \001(\t\"\222\001\n\013ArrowSchema\022#\n\006f"
  "ields\030\001 \003(\0132\023.schema_proto.Field\022,\n\nendi"
  "anness\030\002 \001(\0162\030.schema_proto.Endianness\0220"
  "\n\010metadata\030\003 \001(\0132\036.schema_proto.KeyValue"
  "Metadata\"n\n\006Schema\022/\n\014arrow_schema\030\001 \001(\013"
  "2\031.schema_proto.ArrowSchema\0223\n\016schema_op"
  "tions\030\002 \001(\0132\033.schema_proto.SchemaOptions"
  "*\235\002\n\tLogicType\022\006\n\002NA\020\000\022\010\n\004BOOL\020\001\022\t\n\005UINT"
  "8\020\002\022\010\n\004INT8\020\003\022\n\n\006UINT16\020\004\022\t\n\005INT16\020\005\022\n\n\006"
  "UINT32\020\006\022\t\n\005INT32\020\007\022\n\n\006UINT64\020\010\022\t\n\005INT64"
  "\020\t\022\016\n\nHALF_FLOAT\020\n\022\t\n\005FLOAT\020\013\022\n\n\006DOUBLE\020"
  "\014\022\n\n\006STRING\020\r\022\n\n\006BINARY\020\016\022\025\n\021FIXED_SIZE_"
  "BINARY\020\017\022\010\n\004LIST\020\031\022\n\n\006STRUCT\020\032\022\016\n\nDICTIO"
  "NARY\020\035\022\007\n\003MAP\020\036\022\023\n\017FIXED_SIZE_LIST\020 \022\n\n\006"
  "MAX_ID\020\'*!\n\nEndianness\022\n\n\006Little\020\000\022\007\n\003Bi"
  "g\020\001b\006proto3"
  ;
static ::_pbi::once_flag descriptor_table_schema_5farrow_2eproto_once;
const ::_pbi::DescriptorTable descriptor_table_schema_5farrow_2eproto = {
    false, false, 1491, descriptor_table_protodef_schema_5farrow_2eproto,
    "schema_arrow.proto",
    &descriptor_table_schema_5farrow_2eproto_once, nullptr, 0, 10,
    schemas, file_default_instances, TableStruct_schema_5farrow_2eproto::offsets,
    file_level_metadata_schema_5farrow_2eproto, file_level_enum_descriptors_schema_5farrow_2eproto,
    file_level_service_descriptors_schema_5farrow_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::_pbi::DescriptorTable* descriptor_table_schema_5farrow_2eproto_getter() {
  return &descriptor_table_schema_5farrow_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY2 static ::_pbi::AddDescriptorsRunner dynamic_init_dummy_schema_5farrow_2eproto(&descriptor_table_schema_5farrow_2eproto);
namespace schema_proto {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* LogicType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_schema_5farrow_2eproto);
  return file_level_enum_descriptors_schema_5farrow_2eproto[0];
}
bool LogicType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
    case 12:
    case 13:
    case 14:
    case 15:
    case 25:
    case 26:
    case 29:
    case 30:
    case 32:
    case 39:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Endianness_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_schema_5farrow_2eproto);
  return file_level_enum_descriptors_schema_5farrow_2eproto[1];
}
bool Endianness_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class FixedSizeBinaryType::_Internal {
 public:
};

FixedSizeBinaryType::FixedSizeBinaryType(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:schema_proto.FixedSizeBinaryType)
}
FixedSizeBinaryType::FixedSizeBinaryType(const FixedSizeBinaryType& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  FixedSizeBinaryType* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.byte_width_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _this->_impl_.byte_width_ = from._impl_.byte_width_;
  // @@protoc_insertion_point(copy_constructor:schema_proto.FixedSizeBinaryType)
}

inline void FixedSizeBinaryType::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.byte_width_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

FixedSizeBinaryType::~FixedSizeBinaryType() {
  // @@protoc_insertion_point(destructor:schema_proto.FixedSizeBinaryType)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void FixedSizeBinaryType::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void FixedSizeBinaryType::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void FixedSizeBinaryType::Clear() {
// @@protoc_insertion_point(message_clear_start:schema_proto.FixedSizeBinaryType)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.byte_width_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* FixedSizeBinaryType::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 byte_width = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.byte_width_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* FixedSizeBinaryType::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:schema_proto.FixedSizeBinaryType)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 byte_width = 1;
  if (this->_internal_byte_width() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_byte_width(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:schema_proto.FixedSizeBinaryType)
  return target;
}

size_t FixedSizeBinaryType::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:schema_proto.FixedSizeBinaryType)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 byte_width = 1;
  if (this->_internal_byte_width() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_byte_width());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData FixedSizeBinaryType::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    FixedSizeBinaryType::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*FixedSizeBinaryType::GetClassData() const { return &_class_data_; }


void FixedSizeBinaryType::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<FixedSizeBinaryType*>(&to_msg);
  auto& from = static_cast<const FixedSizeBinaryType&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:schema_proto.FixedSizeBinaryType)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_byte_width() != 0) {
    _this->_internal_set_byte_width(from._internal_byte_width());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void FixedSizeBinaryType::CopyFrom(const FixedSizeBinaryType& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:schema_proto.FixedSizeBinaryType)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FixedSizeBinaryType::IsInitialized() const {
  return true;
}

void FixedSizeBinaryType::InternalSwap(FixedSizeBinaryType* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_.byte_width_, other->_impl_.byte_width_);
}

::PROTOBUF_NAMESPACE_ID::Metadata FixedSizeBinaryType::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_schema_5farrow_2eproto_getter, &descriptor_table_schema_5farrow_2eproto_once,
      file_level_metadata_schema_5farrow_2eproto[0]);
}

// ===================================================================

class FixedSizeListType::_Internal {
 public:
};

FixedSizeListType::FixedSizeListType(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:schema_proto.FixedSizeListType)
}
FixedSizeListType::FixedSizeListType(const FixedSizeListType& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  FixedSizeListType* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.list_size_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _this->_impl_.list_size_ = from._impl_.list_size_;
  // @@protoc_insertion_point(copy_constructor:schema_proto.FixedSizeListType)
}

inline void FixedSizeListType::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.list_size_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

FixedSizeListType::~FixedSizeListType() {
  // @@protoc_insertion_point(destructor:schema_proto.FixedSizeListType)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void FixedSizeListType::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void FixedSizeListType::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void FixedSizeListType::Clear() {
// @@protoc_insertion_point(message_clear_start:schema_proto.FixedSizeListType)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.list_size_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* FixedSizeListType::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 list_size = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.list_size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* FixedSizeListType::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:schema_proto.FixedSizeListType)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 list_size = 1;
  if (this->_internal_list_size() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_list_size(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:schema_proto.FixedSizeListType)
  return target;
}

size_t FixedSizeListType::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:schema_proto.FixedSizeListType)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 list_size = 1;
  if (this->_internal_list_size() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_list_size());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData FixedSizeListType::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    FixedSizeListType::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*FixedSizeListType::GetClassData() const { return &_class_data_; }


void FixedSizeListType::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<FixedSizeListType*>(&to_msg);
  auto& from = static_cast<const FixedSizeListType&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:schema_proto.FixedSizeListType)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_list_size() != 0) {
    _this->_internal_set_list_size(from._internal_list_size());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void FixedSizeListType::CopyFrom(const FixedSizeListType& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:schema_proto.FixedSizeListType)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FixedSizeListType::IsInitialized() const {
  return true;
}

void FixedSizeListType::InternalSwap(FixedSizeListType* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_.list_size_, other->_impl_.list_size_);
}

::PROTOBUF_NAMESPACE_ID::Metadata FixedSizeListType::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_schema_5farrow_2eproto_getter, &descriptor_table_schema_5farrow_2eproto_once,
      file_level_metadata_schema_5farrow_2eproto[1]);
}

// ===================================================================

class DictionaryType::_Internal {
 public:
  static const ::schema_proto::DataType& index_type(const DictionaryType* msg);
  static const ::schema_proto::DataType& value_type(const DictionaryType* msg);
};

const ::schema_proto::DataType&
DictionaryType::_Internal::index_type(const DictionaryType* msg) {
  return *msg->_impl_.index_type_;
}
const ::schema_proto::DataType&
DictionaryType::_Internal::value_type(const DictionaryType* msg) {
  return *msg->_impl_.value_type_;
}
DictionaryType::DictionaryType(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:schema_proto.DictionaryType)
}
DictionaryType::DictionaryType(const DictionaryType& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  DictionaryType* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.index_type_){nullptr}
    , decltype(_impl_.value_type_){nullptr}
    , decltype(_impl_.ordered_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_index_type()) {
    _this->_impl_.index_type_ = new ::schema_proto::DataType(*from._impl_.index_type_);
  }
  if (from._internal_has_value_type()) {
    _this->_impl_.value_type_ = new ::schema_proto::DataType(*from._impl_.value_type_);
  }
  _this->_impl_.ordered_ = from._impl_.ordered_;
  // @@protoc_insertion_point(copy_constructor:schema_proto.DictionaryType)
}

inline void DictionaryType::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.index_type_){nullptr}
    , decltype(_impl_.value_type_){nullptr}
    , decltype(_impl_.ordered_){false}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

DictionaryType::~DictionaryType() {
  // @@protoc_insertion_point(destructor:schema_proto.DictionaryType)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void DictionaryType::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete _impl_.index_type_;
  if (this != internal_default_instance()) delete _impl_.value_type_;
}

void DictionaryType::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void DictionaryType::Clear() {
// @@protoc_insertion_point(message_clear_start:schema_proto.DictionaryType)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && _impl_.index_type_ != nullptr) {
    delete _impl_.index_type_;
  }
  _impl_.index_type_ = nullptr;
  if (GetArenaForAllocation() == nullptr && _impl_.value_type_ != nullptr) {
    delete _impl_.value_type_;
  }
  _impl_.value_type_ = nullptr;
  _impl_.ordered_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DictionaryType::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .schema_proto.DataType index_type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_index_type(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .schema_proto.DataType value_type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_value_type(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool ordered = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.ordered_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DictionaryType::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:schema_proto.DictionaryType)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .schema_proto.DataType index_type = 1;
  if (this->_internal_has_index_type()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, _Internal::index_type(this),
        _Internal::index_type(this).GetCachedSize(), target, stream);
  }

  // .schema_proto.DataType value_type = 2;
  if (this->_internal_has_value_type()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, _Internal::value_type(this),
        _Internal::value_type(this).GetCachedSize(), target, stream);
  }

  // bool ordered = 3;
  if (this->_internal_ordered() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(3, this->_internal_ordered(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:schema_proto.DictionaryType)
  return target;
}

size_t DictionaryType::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:schema_proto.DictionaryType)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .schema_proto.DataType index_type = 1;
  if (this->_internal_has_index_type()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.index_type_);
  }

  // .schema_proto.DataType value_type = 2;
  if (this->_internal_has_value_type()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.value_type_);
  }

  // bool ordered = 3;
  if (this->_internal_ordered() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DictionaryType::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    DictionaryType::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DictionaryType::GetClassData() const { return &_class_data_; }


void DictionaryType::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<DictionaryType*>(&to_msg);
  auto& from = static_cast<const DictionaryType&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:schema_proto.DictionaryType)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_index_type()) {
    _this->_internal_mutable_index_type()->::schema_proto::DataType::MergeFrom(
        from._internal_index_type());
  }
  if (from._internal_has_value_type()) {
    _this->_internal_mutable_value_type()->::schema_proto::DataType::MergeFrom(
        from._internal_value_type());
  }
  if (from._internal_ordered() != 0) {
    _this->_internal_set_ordered(from._internal_ordered());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DictionaryType::CopyFrom(const DictionaryType& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:schema_proto.DictionaryType)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DictionaryType::IsInitialized() const {
  return true;
}

void DictionaryType::InternalSwap(DictionaryType* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DictionaryType, _impl_.ordered_)
      + sizeof(DictionaryType::_impl_.ordered_)
      - PROTOBUF_FIELD_OFFSET(DictionaryType, _impl_.index_type_)>(
          reinterpret_cast<char*>(&_impl_.index_type_),
          reinterpret_cast<char*>(&other->_impl_.index_type_));
}

::PROTOBUF_NAMESPACE_ID::Metadata DictionaryType::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_schema_5farrow_2eproto_getter, &descriptor_table_schema_5farrow_2eproto_once,
      file_level_metadata_schema_5farrow_2eproto[2]);
}

// ===================================================================

class MapType::_Internal {
 public:
};

MapType::MapType(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:schema_proto.MapType)
}
MapType::MapType(const MapType& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  MapType* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.keys_sorted_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _this->_impl_.keys_sorted_ = from._impl_.keys_sorted_;
  // @@protoc_insertion_point(copy_constructor:schema_proto.MapType)
}

inline void MapType::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.keys_sorted_){false}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

MapType::~MapType() {
  // @@protoc_insertion_point(destructor:schema_proto.MapType)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void MapType::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void MapType::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void MapType::Clear() {
// @@protoc_insertion_point(message_clear_start:schema_proto.MapType)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.keys_sorted_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MapType::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool keys_sorted = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.keys_sorted_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* MapType::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:schema_proto.MapType)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool keys_sorted = 1;
  if (this->_internal_keys_sorted() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(1, this->_internal_keys_sorted(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:schema_proto.MapType)
  return target;
}

size_t MapType::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:schema_proto.MapType)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bool keys_sorted = 1;
  if (this->_internal_keys_sorted() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MapType::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    MapType::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MapType::GetClassData() const { return &_class_data_; }


void MapType::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<MapType*>(&to_msg);
  auto& from = static_cast<const MapType&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:schema_proto.MapType)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_keys_sorted() != 0) {
    _this->_internal_set_keys_sorted(from._internal_keys_sorted());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MapType::CopyFrom(const MapType& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:schema_proto.MapType)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MapType::IsInitialized() const {
  return true;
}

void MapType::InternalSwap(MapType* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_.keys_sorted_, other->_impl_.keys_sorted_);
}

::PROTOBUF_NAMESPACE_ID::Metadata MapType::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_schema_5farrow_2eproto_getter, &descriptor_table_schema_5farrow_2eproto_once,
      file_level_metadata_schema_5farrow_2eproto[3]);
}

// ===================================================================

class DataType::_Internal {
 public:
  static const ::schema_proto::FixedSizeBinaryType& fixed_size_binary_type(const DataType* msg);
  static const ::schema_proto::FixedSizeListType& fixed_size_list_type(const DataType* msg);
  static const ::schema_proto::DictionaryType& dictionary_type(const DataType* msg);
  static const ::schema_proto::MapType& map_type(const DataType* msg);
};

const ::schema_proto::FixedSizeBinaryType&
DataType::_Internal::fixed_size_binary_type(const DataType* msg) {
  return *msg->_impl_.type_related_values_.fixed_size_binary_type_;
}
const ::schema_proto::FixedSizeListType&
DataType::_Internal::fixed_size_list_type(const DataType* msg) {
  return *msg->_impl_.type_related_values_.fixed_size_list_type_;
}
const ::schema_proto::DictionaryType&
DataType::_Internal::dictionary_type(const DataType* msg) {
  return *msg->_impl_.type_related_values_.dictionary_type_;
}
const ::schema_proto::MapType&
DataType::_Internal::map_type(const DataType* msg) {
  return *msg->_impl_.type_related_values_.map_type_;
}
void DataType::set_allocated_fixed_size_binary_type(::schema_proto::FixedSizeBinaryType* fixed_size_binary_type) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_type_related_values();
  if (fixed_size_binary_type) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(fixed_size_binary_type);
    if (message_arena != submessage_arena) {
      fixed_size_binary_type = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, fixed_size_binary_type, submessage_arena);
    }
    set_has_fixed_size_binary_type();
    _impl_.type_related_values_.fixed_size_binary_type_ = fixed_size_binary_type;
  }
  // @@protoc_insertion_point(field_set_allocated:schema_proto.DataType.fixed_size_binary_type)
}
void DataType::set_allocated_fixed_size_list_type(::schema_proto::FixedSizeListType* fixed_size_list_type) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_type_related_values();
  if (fixed_size_list_type) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(fixed_size_list_type);
    if (message_arena != submessage_arena) {
      fixed_size_list_type = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, fixed_size_list_type, submessage_arena);
    }
    set_has_fixed_size_list_type();
    _impl_.type_related_values_.fixed_size_list_type_ = fixed_size_list_type;
  }
  // @@protoc_insertion_point(field_set_allocated:schema_proto.DataType.fixed_size_list_type)
}
void DataType::set_allocated_dictionary_type(::schema_proto::DictionaryType* dictionary_type) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_type_related_values();
  if (dictionary_type) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(dictionary_type);
    if (message_arena != submessage_arena) {
      dictionary_type = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, dictionary_type, submessage_arena);
    }
    set_has_dictionary_type();
    _impl_.type_related_values_.dictionary_type_ = dictionary_type;
  }
  // @@protoc_insertion_point(field_set_allocated:schema_proto.DataType.dictionary_type)
}
void DataType::set_allocated_map_type(::schema_proto::MapType* map_type) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_type_related_values();
  if (map_type) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(map_type);
    if (message_arena != submessage_arena) {
      map_type = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, map_type, submessage_arena);
    }
    set_has_map_type();
    _impl_.type_related_values_.map_type_ = map_type;
  }
  // @@protoc_insertion_point(field_set_allocated:schema_proto.DataType.map_type)
}
DataType::DataType(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:schema_proto.DataType)
}
DataType::DataType(const DataType& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  DataType* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.children_){from._impl_.children_}
    , decltype(_impl_.logic_type_){}
    , decltype(_impl_.type_related_values_){}
    , /*decltype(_impl_._cached_size_)*/{}
    , /*decltype(_impl_._oneof_case_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _this->_impl_.logic_type_ = from._impl_.logic_type_;
  clear_has_type_related_values();
  switch (from.type_related_values_case()) {
    case kFixedSizeBinaryType: {
      _this->_internal_mutable_fixed_size_binary_type()->::schema_proto::FixedSizeBinaryType::MergeFrom(
          from._internal_fixed_size_binary_type());
      break;
    }
    case kFixedSizeListType: {
      _this->_internal_mutable_fixed_size_list_type()->::schema_proto::FixedSizeListType::MergeFrom(
          from._internal_fixed_size_list_type());
      break;
    }
    case kDictionaryType: {
      _this->_internal_mutable_dictionary_type()->::schema_proto::DictionaryType::MergeFrom(
          from._internal_dictionary_type());
      break;
    }
    case kMapType: {
      _this->_internal_mutable_map_type()->::schema_proto::MapType::MergeFrom(
          from._internal_map_type());
      break;
    }
    case TYPE_RELATED_VALUES_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:schema_proto.DataType)
}

inline void DataType::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.children_){arena}
    , decltype(_impl_.logic_type_){0}
    , decltype(_impl_.type_related_values_){}
    , /*decltype(_impl_._cached_size_)*/{}
    , /*decltype(_impl_._oneof_case_)*/{}
  };
  clear_has_type_related_values();
}

DataType::~DataType() {
  // @@protoc_insertion_point(destructor:schema_proto.DataType)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void DataType::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.children_.~RepeatedPtrField();
  if (has_type_related_values()) {
    clear_type_related_values();
  }
}

void DataType::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void DataType::clear_type_related_values() {
// @@protoc_insertion_point(one_of_clear_start:schema_proto.DataType)
  switch (type_related_values_case()) {
    case kFixedSizeBinaryType: {
      if (GetArenaForAllocation() == nullptr) {
        delete _impl_.type_related_values_.fixed_size_binary_type_;
      }
      break;
    }
    case kFixedSizeListType: {
      if (GetArenaForAllocation() == nullptr) {
        delete _impl_.type_related_values_.fixed_size_list_type_;
      }
      break;
    }
    case kDictionaryType: {
      if (GetArenaForAllocation() == nullptr) {
        delete _impl_.type_related_values_.dictionary_type_;
      }
      break;
    }
    case kMapType: {
      if (GetArenaForAllocation() == nullptr) {
        delete _impl_.type_related_values_.map_type_;
      }
      break;
    }
    case TYPE_RELATED_VALUES_NOT_SET: {
      break;
    }
  }
  _impl_._oneof_case_[0] = TYPE_RELATED_VALUES_NOT_SET;
}


void DataType::Clear() {
// @@protoc_insertion_point(message_clear_start:schema_proto.DataType)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.children_.Clear();
  _impl_.logic_type_ = 0;
  clear_type_related_values();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DataType::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .schema_proto.FixedSizeBinaryType fixed_size_binary_type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_fixed_size_binary_type(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .schema_proto.FixedSizeListType fixed_size_list_type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_fixed_size_list_type(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .schema_proto.DictionaryType dictionary_type = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_dictionary_type(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .schema_proto.MapType map_type = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_map_type(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .schema_proto.LogicType logic_type = 100;
      case 100:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_logic_type(static_cast<::schema_proto::LogicType>(val));
        } else
          goto handle_unusual;
        continue;
      // repeated .schema_proto.Field children = 101;
      case 101:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr -= 2;
          do {
            ptr += 2;
            ptr = ctx->ParseMessage(_internal_add_children(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<810>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DataType::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:schema_proto.DataType)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .schema_proto.FixedSizeBinaryType fixed_size_binary_type = 1;
  if (_internal_has_fixed_size_binary_type()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, _Internal::fixed_size_binary_type(this),
        _Internal::fixed_size_binary_type(this).GetCachedSize(), target, stream);
  }

  // .schema_proto.FixedSizeListType fixed_size_list_type = 2;
  if (_internal_has_fixed_size_list_type()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, _Internal::fixed_size_list_type(this),
        _Internal::fixed_size_list_type(this).GetCachedSize(), target, stream);
  }

  // .schema_proto.DictionaryType dictionary_type = 3;
  if (_internal_has_dictionary_type()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, _Internal::dictionary_type(this),
        _Internal::dictionary_type(this).GetCachedSize(), target, stream);
  }

  // .schema_proto.MapType map_type = 4;
  if (_internal_has_map_type()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(4, _Internal::map_type(this),
        _Internal::map_type(this).GetCachedSize(), target, stream);
  }

  // .schema_proto.LogicType logic_type = 100;
  if (this->_internal_logic_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      100, this->_internal_logic_type(), target);
  }

  // repeated .schema_proto.Field children = 101;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_children_size()); i < n; i++) {
    const auto& repfield = this->_internal_children(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(101, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:schema_proto.DataType)
  return target;
}

size_t DataType::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:schema_proto.DataType)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .schema_proto.Field children = 101;
  total_size += 2UL * this->_internal_children_size();
  for (const auto& msg : this->_impl_.children_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .schema_proto.LogicType logic_type = 100;
  if (this->_internal_logic_type() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::EnumSize(this->_internal_logic_type());
  }

  switch (type_related_values_case()) {
    // .schema_proto.FixedSizeBinaryType fixed_size_binary_type = 1;
    case kFixedSizeBinaryType: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *_impl_.type_related_values_.fixed_size_binary_type_);
      break;
    }
    // .schema_proto.FixedSizeListType fixed_size_list_type = 2;
    case kFixedSizeListType: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *_impl_.type_related_values_.fixed_size_list_type_);
      break;
    }
    // .schema_proto.DictionaryType dictionary_type = 3;
    case kDictionaryType: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *_impl_.type_related_values_.dictionary_type_);
      break;
    }
    // .schema_proto.MapType map_type = 4;
    case kMapType: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *_impl_.type_related_values_.map_type_);
      break;
    }
    case TYPE_RELATED_VALUES_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DataType::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    DataType::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DataType::GetClassData() const { return &_class_data_; }


void DataType::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<DataType*>(&to_msg);
  auto& from = static_cast<const DataType&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:schema_proto.DataType)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.children_.MergeFrom(from._impl_.children_);
  if (from._internal_logic_type() != 0) {
    _this->_internal_set_logic_type(from._internal_logic_type());
  }
  switch (from.type_related_values_case()) {
    case kFixedSizeBinaryType: {
      _this->_internal_mutable_fixed_size_binary_type()->::schema_proto::FixedSizeBinaryType::MergeFrom(
          from._internal_fixed_size_binary_type());
      break;
    }
    case kFixedSizeListType: {
      _this->_internal_mutable_fixed_size_list_type()->::schema_proto::FixedSizeListType::MergeFrom(
          from._internal_fixed_size_list_type());
      break;
    }
    case kDictionaryType: {
      _this->_internal_mutable_dictionary_type()->::schema_proto::DictionaryType::MergeFrom(
          from._internal_dictionary_type());
      break;
    }
    case kMapType: {
      _this->_internal_mutable_map_type()->::schema_proto::MapType::MergeFrom(
          from._internal_map_type());
      break;
    }
    case TYPE_RELATED_VALUES_NOT_SET: {
      break;
    }
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DataType::CopyFrom(const DataType& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:schema_proto.DataType)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DataType::IsInitialized() const {
  return true;
}

void DataType::InternalSwap(DataType* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.children_.InternalSwap(&other->_impl_.children_);
  swap(_impl_.logic_type_, other->_impl_.logic_type_);
  swap(_impl_.type_related_values_, other->_impl_.type_related_values_);
  swap(_impl_._oneof_case_[0], other->_impl_._oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata DataType::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_schema_5farrow_2eproto_getter, &descriptor_table_schema_5farrow_2eproto_once,
      file_level_metadata_schema_5farrow_2eproto[4]);
}

// ===================================================================

class KeyValueMetadata::_Internal {
 public:
};

KeyValueMetadata::KeyValueMetadata(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:schema_proto.KeyValueMetadata)
}
KeyValueMetadata::KeyValueMetadata(const KeyValueMetadata& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  KeyValueMetadata* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.keys_){from._impl_.keys_}
    , decltype(_impl_.values_){from._impl_.values_}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:schema_proto.KeyValueMetadata)
}

inline void KeyValueMetadata::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.keys_){arena}
    , decltype(_impl_.values_){arena}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

KeyValueMetadata::~KeyValueMetadata() {
  // @@protoc_insertion_point(destructor:schema_proto.KeyValueMetadata)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void KeyValueMetadata::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.keys_.~RepeatedPtrField();
  _impl_.values_.~RepeatedPtrField();
}

void KeyValueMetadata::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void KeyValueMetadata::Clear() {
// @@protoc_insertion_point(message_clear_start:schema_proto.KeyValueMetadata)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.keys_.Clear();
  _impl_.values_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* KeyValueMetadata::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated string keys = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_keys();
            ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(ptr);
            CHK_(::_pbi::VerifyUTF8(str, "schema_proto.KeyValueMetadata.keys"));
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated string values = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_values();
            ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(ptr);
            CHK_(::_pbi::VerifyUTF8(str, "schema_proto.KeyValueMetadata.values"));
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* KeyValueMetadata::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:schema_proto.KeyValueMetadata)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string keys = 1;
  for (int i = 0, n = this->_internal_keys_size(); i < n; i++) {
    const auto& s = this->_internal_keys(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "schema_proto.KeyValueMetadata.keys");
    target = stream->WriteString(1, s, target);
  }

  // repeated string values = 2;
  for (int i = 0, n = this->_internal_values_size(); i < n; i++) {
    const auto& s = this->_internal_values(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "schema_proto.KeyValueMetadata.values");
    target = stream->WriteString(2, s, target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:schema_proto.KeyValueMetadata)
  return target;
}

size_t KeyValueMetadata::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:schema_proto.KeyValueMetadata)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string keys = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(_impl_.keys_.size());
  for (int i = 0, n = _impl_.keys_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      _impl_.keys_.Get(i));
  }

  // repeated string values = 2;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(_impl_.values_.size());
  for (int i = 0, n = _impl_.values_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      _impl_.values_.Get(i));
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData KeyValueMetadata::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    KeyValueMetadata::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*KeyValueMetadata::GetClassData() const { return &_class_data_; }


void KeyValueMetadata::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<KeyValueMetadata*>(&to_msg);
  auto& from = static_cast<const KeyValueMetadata&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:schema_proto.KeyValueMetadata)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.keys_.MergeFrom(from._impl_.keys_);
  _this->_impl_.values_.MergeFrom(from._impl_.values_);
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void KeyValueMetadata::CopyFrom(const KeyValueMetadata& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:schema_proto.KeyValueMetadata)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool KeyValueMetadata::IsInitialized() const {
  return true;
}

void KeyValueMetadata::InternalSwap(KeyValueMetadata* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.keys_.InternalSwap(&other->_impl_.keys_);
  _impl_.values_.InternalSwap(&other->_impl_.values_);
}

::PROTOBUF_NAMESPACE_ID::Metadata KeyValueMetadata::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_schema_5farrow_2eproto_getter, &descriptor_table_schema_5farrow_2eproto_once,
      file_level_metadata_schema_5farrow_2eproto[5]);
}

// ===================================================================

class Field::_Internal {
 public:
  static const ::schema_proto::DataType& data_type(const Field* msg);
  static const ::schema_proto::KeyValueMetadata& metadata(const Field* msg);
};

const ::schema_proto::DataType&
Field::_Internal::data_type(const Field* msg) {
  return *msg->_impl_.data_type_;
}
const ::schema_proto::KeyValueMetadata&
Field::_Internal::metadata(const Field* msg) {
  return *msg->_impl_.metadata_;
}
Field::Field(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:schema_proto.Field)
}
Field::Field(const Field& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Field* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.name_){}
    , decltype(_impl_.data_type_){nullptr}
    , decltype(_impl_.metadata_){nullptr}
    , decltype(_impl_.nullable_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    _this->_impl_.name_.Set(from._internal_name(), 
      _this->GetArenaForAllocation());
  }
  if (from._internal_has_data_type()) {
    _this->_impl_.data_type_ = new ::schema_proto::DataType(*from._impl_.data_type_);
  }
  if (from._internal_has_metadata()) {
    _this->_impl_.metadata_ = new ::schema_proto::KeyValueMetadata(*from._impl_.metadata_);
  }
  _this->_impl_.nullable_ = from._impl_.nullable_;
  // @@protoc_insertion_point(copy_constructor:schema_proto.Field)
}

inline void Field::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.name_){}
    , decltype(_impl_.data_type_){nullptr}
    , decltype(_impl_.metadata_){nullptr}
    , decltype(_impl_.nullable_){false}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

Field::~Field() {
  // @@protoc_insertion_point(destructor:schema_proto.Field)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Field::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.name_.Destroy();
  if (this != internal_default_instance()) delete _impl_.data_type_;
  if (this != internal_default_instance()) delete _impl_.metadata_;
}

void Field::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Field::Clear() {
// @@protoc_insertion_point(message_clear_start:schema_proto.Field)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.name_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && _impl_.data_type_ != nullptr) {
    delete _impl_.data_type_;
  }
  _impl_.data_type_ = nullptr;
  if (GetArenaForAllocation() == nullptr && _impl_.metadata_ != nullptr) {
    delete _impl_.metadata_;
  }
  _impl_.metadata_ = nullptr;
  _impl_.nullable_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Field::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "schema_proto.Field.name"));
        } else
          goto handle_unusual;
        continue;
      // bool nullable = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.nullable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .schema_proto.DataType data_type = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_data_type(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .schema_proto.KeyValueMetadata metadata = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_metadata(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Field::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:schema_proto.Field)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "schema_proto.Field.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  // bool nullable = 2;
  if (this->_internal_nullable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(2, this->_internal_nullable(), target);
  }

  // .schema_proto.DataType data_type = 3;
  if (this->_internal_has_data_type()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, _Internal::data_type(this),
        _Internal::data_type(this).GetCachedSize(), target, stream);
  }

  // .schema_proto.KeyValueMetadata metadata = 4;
  if (this->_internal_has_metadata()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(4, _Internal::metadata(this),
        _Internal::metadata(this).GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:schema_proto.Field)
  return target;
}

size_t Field::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:schema_proto.Field)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // .schema_proto.DataType data_type = 3;
  if (this->_internal_has_data_type()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.data_type_);
  }

  // .schema_proto.KeyValueMetadata metadata = 4;
  if (this->_internal_has_metadata()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.metadata_);
  }

  // bool nullable = 2;
  if (this->_internal_nullable() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Field::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Field::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Field::GetClassData() const { return &_class_data_; }


void Field::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Field*>(&to_msg);
  auto& from = static_cast<const Field&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:schema_proto.Field)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _this->_internal_set_name(from._internal_name());
  }
  if (from._internal_has_data_type()) {
    _this->_internal_mutable_data_type()->::schema_proto::DataType::MergeFrom(
        from._internal_data_type());
  }
  if (from._internal_has_metadata()) {
    _this->_internal_mutable_metadata()->::schema_proto::KeyValueMetadata::MergeFrom(
        from._internal_metadata());
  }
  if (from._internal_nullable() != 0) {
    _this->_internal_set_nullable(from._internal_nullable());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Field::CopyFrom(const Field& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:schema_proto.Field)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Field::IsInitialized() const {
  return true;
}

void Field::InternalSwap(Field* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.name_, lhs_arena,
      &other->_impl_.name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Field, _impl_.nullable_)
      + sizeof(Field::_impl_.nullable_)
      - PROTOBUF_FIELD_OFFSET(Field, _impl_.data_type_)>(
          reinterpret_cast<char*>(&_impl_.data_type_),
          reinterpret_cast<char*>(&other->_impl_.data_type_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Field::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_schema_5farrow_2eproto_getter, &descriptor_table_schema_5farrow_2eproto_once,
      file_level_metadata_schema_5farrow_2eproto[6]);
}

// ===================================================================

class SchemaOptions::_Internal {
 public:
};

SchemaOptions::SchemaOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:schema_proto.SchemaOptions)
}
SchemaOptions::SchemaOptions(const SchemaOptions& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  SchemaOptions* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.primary_column_){}
    , decltype(_impl_.version_column_){}
    , decltype(_impl_.vector_column_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.primary_column_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.primary_column_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_primary_column().empty()) {
    _this->_impl_.primary_column_.Set(from._internal_primary_column(), 
      _this->GetArenaForAllocation());
  }
  _impl_.version_column_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.version_column_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_version_column().empty()) {
    _this->_impl_.version_column_.Set(from._internal_version_column(), 
      _this->GetArenaForAllocation());
  }
  _impl_.vector_column_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.vector_column_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_vector_column().empty()) {
    _this->_impl_.vector_column_.Set(from._internal_vector_column(), 
      _this->GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:schema_proto.SchemaOptions)
}

inline void SchemaOptions::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.primary_column_){}
    , decltype(_impl_.version_column_){}
    , decltype(_impl_.vector_column_){}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.primary_column_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.primary_column_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.version_column_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.version_column_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.vector_column_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.vector_column_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

SchemaOptions::~SchemaOptions() {
  // @@protoc_insertion_point(destructor:schema_proto.SchemaOptions)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void SchemaOptions::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.primary_column_.Destroy();
  _impl_.version_column_.Destroy();
  _impl_.vector_column_.Destroy();
}

void SchemaOptions::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void SchemaOptions::Clear() {
// @@protoc_insertion_point(message_clear_start:schema_proto.SchemaOptions)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.primary_column_.ClearToEmpty();
  _impl_.version_column_.ClearToEmpty();
  _impl_.vector_column_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SchemaOptions::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string primary_column = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_primary_column();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "schema_proto.SchemaOptions.primary_column"));
        } else
          goto handle_unusual;
        continue;
      // string version_column = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_version_column();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "schema_proto.SchemaOptions.version_column"));
        } else
          goto handle_unusual;
        continue;
      // string vector_column = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_vector_column();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "schema_proto.SchemaOptions.vector_column"));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SchemaOptions::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:schema_proto.SchemaOptions)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string primary_column = 1;
  if (!this->_internal_primary_column().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_primary_column().data(), static_cast<int>(this->_internal_primary_column().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "schema_proto.SchemaOptions.primary_column");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_primary_column(), target);
  }

  // string version_column = 2;
  if (!this->_internal_version_column().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_version_column().data(), static_cast<int>(this->_internal_version_column().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "schema_proto.SchemaOptions.version_column");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_version_column(), target);
  }

  // string vector_column = 3;
  if (!this->_internal_vector_column().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_vector_column().data(), static_cast<int>(this->_internal_vector_column().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "schema_proto.SchemaOptions.vector_column");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_vector_column(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:schema_proto.SchemaOptions)
  return target;
}

size_t SchemaOptions::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:schema_proto.SchemaOptions)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string primary_column = 1;
  if (!this->_internal_primary_column().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_primary_column());
  }

  // string version_column = 2;
  if (!this->_internal_version_column().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_version_column());
  }

  // string vector_column = 3;
  if (!this->_internal_vector_column().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_vector_column());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SchemaOptions::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    SchemaOptions::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SchemaOptions::GetClassData() const { return &_class_data_; }


void SchemaOptions::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<SchemaOptions*>(&to_msg);
  auto& from = static_cast<const SchemaOptions&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:schema_proto.SchemaOptions)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_primary_column().empty()) {
    _this->_internal_set_primary_column(from._internal_primary_column());
  }
  if (!from._internal_version_column().empty()) {
    _this->_internal_set_version_column(from._internal_version_column());
  }
  if (!from._internal_vector_column().empty()) {
    _this->_internal_set_vector_column(from._internal_vector_column());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SchemaOptions::CopyFrom(const SchemaOptions& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:schema_proto.SchemaOptions)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SchemaOptions::IsInitialized() const {
  return true;
}

void SchemaOptions::InternalSwap(SchemaOptions* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.primary_column_, lhs_arena,
      &other->_impl_.primary_column_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.version_column_, lhs_arena,
      &other->_impl_.version_column_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.vector_column_, lhs_arena,
      &other->_impl_.vector_column_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata SchemaOptions::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_schema_5farrow_2eproto_getter, &descriptor_table_schema_5farrow_2eproto_once,
      file_level_metadata_schema_5farrow_2eproto[7]);
}

// ===================================================================

class ArrowSchema::_Internal {
 public:
  static const ::schema_proto::KeyValueMetadata& metadata(const ArrowSchema* msg);
};

const ::schema_proto::KeyValueMetadata&
ArrowSchema::_Internal::metadata(const ArrowSchema* msg) {
  return *msg->_impl_.metadata_;
}
ArrowSchema::ArrowSchema(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:schema_proto.ArrowSchema)
}
ArrowSchema::ArrowSchema(const ArrowSchema& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  ArrowSchema* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.fields_){from._impl_.fields_}
    , decltype(_impl_.metadata_){nullptr}
    , decltype(_impl_.endianness_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_metadata()) {
    _this->_impl_.metadata_ = new ::schema_proto::KeyValueMetadata(*from._impl_.metadata_);
  }
  _this->_impl_.endianness_ = from._impl_.endianness_;
  // @@protoc_insertion_point(copy_constructor:schema_proto.ArrowSchema)
}

inline void ArrowSchema::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.fields_){arena}
    , decltype(_impl_.metadata_){nullptr}
    , decltype(_impl_.endianness_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

ArrowSchema::~ArrowSchema() {
  // @@protoc_insertion_point(destructor:schema_proto.ArrowSchema)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void ArrowSchema::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.fields_.~RepeatedPtrField();
  if (this != internal_default_instance()) delete _impl_.metadata_;
}

void ArrowSchema::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void ArrowSchema::Clear() {
// @@protoc_insertion_point(message_clear_start:schema_proto.ArrowSchema)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.fields_.Clear();
  if (GetArenaForAllocation() == nullptr && _impl_.metadata_ != nullptr) {
    delete _impl_.metadata_;
  }
  _impl_.metadata_ = nullptr;
  _impl_.endianness_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ArrowSchema::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .schema_proto.Field fields = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_fields(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .schema_proto.Endianness endianness = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_endianness(static_cast<::schema_proto::Endianness>(val));
        } else
          goto handle_unusual;
        continue;
      // .schema_proto.KeyValueMetadata metadata = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_metadata(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ArrowSchema::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:schema_proto.ArrowSchema)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .schema_proto.Field fields = 1;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_fields_size()); i < n; i++) {
    const auto& repfield = this->_internal_fields(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(1, repfield, repfield.GetCachedSize(), target, stream);
  }

  // .schema_proto.Endianness endianness = 2;
  if (this->_internal_endianness() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      2, this->_internal_endianness(), target);
  }

  // .schema_proto.KeyValueMetadata metadata = 3;
  if (this->_internal_has_metadata()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, _Internal::metadata(this),
        _Internal::metadata(this).GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:schema_proto.ArrowSchema)
  return target;
}

size_t ArrowSchema::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:schema_proto.ArrowSchema)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .schema_proto.Field fields = 1;
  total_size += 1UL * this->_internal_fields_size();
  for (const auto& msg : this->_impl_.fields_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .schema_proto.KeyValueMetadata metadata = 3;
  if (this->_internal_has_metadata()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.metadata_);
  }

  // .schema_proto.Endianness endianness = 2;
  if (this->_internal_endianness() != 0) {
    total_size += 1 +
      ::_pbi::WireFormatLite::EnumSize(this->_internal_endianness());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ArrowSchema::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    ArrowSchema::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ArrowSchema::GetClassData() const { return &_class_data_; }


void ArrowSchema::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<ArrowSchema*>(&to_msg);
  auto& from = static_cast<const ArrowSchema&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:schema_proto.ArrowSchema)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.fields_.MergeFrom(from._impl_.fields_);
  if (from._internal_has_metadata()) {
    _this->_internal_mutable_metadata()->::schema_proto::KeyValueMetadata::MergeFrom(
        from._internal_metadata());
  }
  if (from._internal_endianness() != 0) {
    _this->_internal_set_endianness(from._internal_endianness());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ArrowSchema::CopyFrom(const ArrowSchema& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:schema_proto.ArrowSchema)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ArrowSchema::IsInitialized() const {
  return true;
}

void ArrowSchema::InternalSwap(ArrowSchema* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.fields_.InternalSwap(&other->_impl_.fields_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ArrowSchema, _impl_.endianness_)
      + sizeof(ArrowSchema::_impl_.endianness_)
      - PROTOBUF_FIELD_OFFSET(ArrowSchema, _impl_.metadata_)>(
          reinterpret_cast<char*>(&_impl_.metadata_),
          reinterpret_cast<char*>(&other->_impl_.metadata_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ArrowSchema::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_schema_5farrow_2eproto_getter, &descriptor_table_schema_5farrow_2eproto_once,
      file_level_metadata_schema_5farrow_2eproto[8]);
}

// ===================================================================

class Schema::_Internal {
 public:
  static const ::schema_proto::ArrowSchema& arrow_schema(const Schema* msg);
  static const ::schema_proto::SchemaOptions& schema_options(const Schema* msg);
};

const ::schema_proto::ArrowSchema&
Schema::_Internal::arrow_schema(const Schema* msg) {
  return *msg->_impl_.arrow_schema_;
}
const ::schema_proto::SchemaOptions&
Schema::_Internal::schema_options(const Schema* msg) {
  return *msg->_impl_.schema_options_;
}
Schema::Schema(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:schema_proto.Schema)
}
Schema::Schema(const Schema& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Schema* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.arrow_schema_){nullptr}
    , decltype(_impl_.schema_options_){nullptr}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_arrow_schema()) {
    _this->_impl_.arrow_schema_ = new ::schema_proto::ArrowSchema(*from._impl_.arrow_schema_);
  }
  if (from._internal_has_schema_options()) {
    _this->_impl_.schema_options_ = new ::schema_proto::SchemaOptions(*from._impl_.schema_options_);
  }
  // @@protoc_insertion_point(copy_constructor:schema_proto.Schema)
}

inline void Schema::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.arrow_schema_){nullptr}
    , decltype(_impl_.schema_options_){nullptr}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Schema::~Schema() {
  // @@protoc_insertion_point(destructor:schema_proto.Schema)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Schema::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete _impl_.arrow_schema_;
  if (this != internal_default_instance()) delete _impl_.schema_options_;
}

void Schema::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Schema::Clear() {
// @@protoc_insertion_point(message_clear_start:schema_proto.Schema)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && _impl_.arrow_schema_ != nullptr) {
    delete _impl_.arrow_schema_;
  }
  _impl_.arrow_schema_ = nullptr;
  if (GetArenaForAllocation() == nullptr && _impl_.schema_options_ != nullptr) {
    delete _impl_.schema_options_;
  }
  _impl_.schema_options_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Schema::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .schema_proto.ArrowSchema arrow_schema = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_arrow_schema(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .schema_proto.SchemaOptions schema_options = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_schema_options(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Schema::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:schema_proto.Schema)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .schema_proto.ArrowSchema arrow_schema = 1;
  if (this->_internal_has_arrow_schema()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, _Internal::arrow_schema(this),
        _Internal::arrow_schema(this).GetCachedSize(), target, stream);
  }

  // .schema_proto.SchemaOptions schema_options = 2;
  if (this->_internal_has_schema_options()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, _Internal::schema_options(this),
        _Internal::schema_options(this).GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:schema_proto.Schema)
  return target;
}

size_t Schema::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:schema_proto.Schema)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .schema_proto.ArrowSchema arrow_schema = 1;
  if (this->_internal_has_arrow_schema()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.arrow_schema_);
  }

  // .schema_proto.SchemaOptions schema_options = 2;
  if (this->_internal_has_schema_options()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.schema_options_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Schema::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Schema::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Schema::GetClassData() const { return &_class_data_; }


void Schema::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Schema*>(&to_msg);
  auto& from = static_cast<const Schema&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:schema_proto.Schema)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_arrow_schema()) {
    _this->_internal_mutable_arrow_schema()->::schema_proto::ArrowSchema::MergeFrom(
        from._internal_arrow_schema());
  }
  if (from._internal_has_schema_options()) {
    _this->_internal_mutable_schema_options()->::schema_proto::SchemaOptions::MergeFrom(
        from._internal_schema_options());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Schema::CopyFrom(const Schema& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:schema_proto.Schema)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Schema::IsInitialized() const {
  return true;
}

void Schema::InternalSwap(Schema* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Schema, _impl_.schema_options_)
      + sizeof(Schema::_impl_.schema_options_)
      - PROTOBUF_FIELD_OFFSET(Schema, _impl_.arrow_schema_)>(
          reinterpret_cast<char*>(&_impl_.arrow_schema_),
          reinterpret_cast<char*>(&other->_impl_.arrow_schema_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Schema::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_schema_5farrow_2eproto_getter, &descriptor_table_schema_5farrow_2eproto_once,
      file_level_metadata_schema_5farrow_2eproto[9]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace schema_proto
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::schema_proto::FixedSizeBinaryType*
Arena::CreateMaybeMessage< ::schema_proto::FixedSizeBinaryType >(Arena* arena) {
  return Arena::CreateMessageInternal< ::schema_proto::FixedSizeBinaryType >(arena);
}
template<> PROTOBUF_NOINLINE ::schema_proto::FixedSizeListType*
Arena::CreateMaybeMessage< ::schema_proto::FixedSizeListType >(Arena* arena) {
  return Arena::CreateMessageInternal< ::schema_proto::FixedSizeListType >(arena);
}
template<> PROTOBUF_NOINLINE ::schema_proto::DictionaryType*
Arena::CreateMaybeMessage< ::schema_proto::DictionaryType >(Arena* arena) {
  return Arena::CreateMessageInternal< ::schema_proto::DictionaryType >(arena);
}
template<> PROTOBUF_NOINLINE ::schema_proto::MapType*
Arena::CreateMaybeMessage< ::schema_proto::MapType >(Arena* arena) {
  return Arena::CreateMessageInternal< ::schema_proto::MapType >(arena);
}
template<> PROTOBUF_NOINLINE ::schema_proto::DataType*
Arena::CreateMaybeMessage< ::schema_proto::DataType >(Arena* arena) {
  return Arena::CreateMessageInternal< ::schema_proto::DataType >(arena);
}
template<> PROTOBUF_NOINLINE ::schema_proto::KeyValueMetadata*
Arena::CreateMaybeMessage< ::schema_proto::KeyValueMetadata >(Arena* arena) {
  return Arena::CreateMessageInternal< ::schema_proto::KeyValueMetadata >(arena);
}
template<> PROTOBUF_NOINLINE ::schema_proto::Field*
Arena::CreateMaybeMessage< ::schema_proto::Field >(Arena* arena) {
  return Arena::CreateMessageInternal< ::schema_proto::Field >(arena);
}
template<> PROTOBUF_NOINLINE ::schema_proto::SchemaOptions*
Arena::CreateMaybeMessage< ::schema_proto::SchemaOptions >(Arena* arena) {
  return Arena::CreateMessageInternal< ::schema_proto::SchemaOptions >(arena);
}
template<> PROTOBUF_NOINLINE ::schema_proto::ArrowSchema*
Arena::CreateMaybeMessage< ::schema_proto::ArrowSchema >(Arena* arena) {
  return Arena::CreateMessageInternal< ::schema_proto::ArrowSchema >(arena);
}
template<> PROTOBUF_NOINLINE ::schema_proto::Schema*
Arena::CreateMaybeMessage< ::schema_proto::Schema >(Arena* arena) {
  return Arena::CreateMessageInternal< ::schema_proto::Schema >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
