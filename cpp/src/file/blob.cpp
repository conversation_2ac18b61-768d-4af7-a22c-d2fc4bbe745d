// Copyright 2023 Zilliz
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "milvus-storage/file/blob.h"
#include <memory>
#include "proto/manifest.pb.h"

namespace milvus_storage {

std::unique_ptr<manifest_proto::Blob> Blob::ToProtobuf() const {
  auto blob = std::make_unique<manifest_proto::Blob>();
  blob->set_name(name);
  blob->set_size(size);
  blob->set_file(file);
  return blob;
}

Blob Blob::FromProtobuf(const manifest_proto::Blob blob) {
  Blob ret;
  ret.name = blob.name();
  ret.size = blob.size();
  ret.file = blob.file();
  return ret;
}

}  // namespace milvus_storage
