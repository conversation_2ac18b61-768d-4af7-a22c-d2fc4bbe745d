name: cpp

on:
  push:
   paths:
    - 'cpp/**'
    - '!go/**'
  pull_request:
   paths:
    - 'cpp/**'
    - '!go/**'
jobs:
  unittest:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v3

      - name: Install dependencies
        uses: aminya/setup-cpp@v1
        with: 
          conan: 1.61.0
          cmake: true

      - name: setup conan
        run: 
          conan remote add default-conan-local https://milvus01.jfrog.io/artifactory/api/conan/default-conan-local --insert
          && conan remote list
      
      - name: conan package cache
        uses: actions/cache@v3
        with:
          path: ~/.conan
          key: conan-${{ hashFiles('./cpp/conanfile.py') }}
          restore-keys: conan-

      - name: Build
        working-directory: ./cpp
        run: 
          make build USE_ASAN=True
      # - uses: actions/cache/save@v3
      #   if: always()
      #   with:
      #     path: ~/.conan
      #     key: conan-${{ hashFiles('./cpp/conanfile.py') }}
     
      - name: check-format
        working-directory: ./cpp
        run: 
          make check-format

      - name: check-tidy
        working-directory: ./cpp
        run:
          make check-tidy

      - name : Test
        working-directory: ./cpp
        run: 
          make test USE_ASAN=True

